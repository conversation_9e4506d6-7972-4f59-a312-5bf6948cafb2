#!/usr/bin/env python3
"""
Инициализация схемы Oracle AJD для ChartGenius
Создает все необходимые таблицы и индексы для продакшена
"""

import os
import sys
import logging
from datetime import datetime

# Добавляем путь к backend для импорта модулей
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from config.oracle_config import oracle_config
import oracledb

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OracleSchemaInitializer:
    """Инициализатор схемы Oracle AJD для ChartGenius"""
    
    def __init__(self):
        self.connection = None
        self._connect()
    
    def _connect(self):
        """Подключение к Oracle AJD"""
        try:
            # Используем wallet для подключения
            wallet_location = os.path.join(os.path.dirname(os.path.dirname(__file__)), "oracle_wallet")
            
            self.connection = oracledb.connect(
                user=oracle_config.username,
                password=oracle_config.password,
                dsn="chartgenius2_high",  # Из tnsnames.ora
                config_dir=wallet_location,
                wallet_location=wallet_location,
                wallet_password="ChartGenius2025!"
            )
            
            logger.info("✅ Подключение к Oracle AJD установлено")
            
        except Exception as e:
            logger.error(f"❌ Ошибка подключения к Oracle AJD: {e}")
            raise
    
    def create_tables(self):
        """Создание всех таблиц для ChartGenius"""
        
        tables = {
            # Пользователи Telegram
            "users": """
                CREATE TABLE users (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    telegram_id NUMBER(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.telegram_id' RETURNING NUMBER)),
                    username VARCHAR2(500) GENERATED ALWAYS AS (JSON_VALUE(data, '$.username')),
                    role VARCHAR2(50) GENERATED ALWAYS AS (JSON_VALUE(data, '$.role')),
                    tier VARCHAR2(50) GENERATED ALWAYS AS (JSON_VALUE(data, '$.tier')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Подписки пользователей
            "subscriptions": """
                CREATE TABLE subscriptions (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    telegram_id NUMBER(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.telegram_id' RETURNING NUMBER)),
                    level VARCHAR2(50) GENERATED ALWAYS AS (JSON_VALUE(data, '$.level')),
                    expires_at TIMESTAMP GENERATED ALWAYS AS (TO_TIMESTAMP(JSON_VALUE(data, '$.expires_at'), 'YYYY-MM-DD"T"HH24:MI:SS.FF"Z"')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Списки отслеживания пользователей
            "watchlists": """
                CREATE TABLE watchlists (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    telegram_id NUMBER(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.telegram_id' RETURNING NUMBER)),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Баны пользователей
            "bans": """
                CREATE TABLE bans (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    telegram_id NUMBER(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.telegram_id' RETURNING NUMBER)),
                    moderator_id NUMBER(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.moderator_id' RETURNING NUMBER)),
                    expires_at TIMESTAMP GENERATED ALWAYS AS (TO_TIMESTAMP(JSON_VALUE(data, '$.expires_at'), 'YYYY-MM-DD"T"HH24:MI:SS.FF"Z"')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Результаты анализов
            "analyses": """
                CREATE TABLE analyses (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    owner_id NUMBER(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.owner_id' RETURNING NUMBER)),
                    symbol VARCHAR2(50) GENERATED ALWAYS AS (JSON_VALUE(data, '$.symbol')),
                    interval_val VARCHAR2(50) GENERATED ALWAYS AS (JSON_VALUE(data, '$.interval')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Флаги на анализы
            "flags": """
                CREATE TABLE flags (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    analysis_id VARCHAR2(50) GENERATED ALWAYS AS (JSON_VALUE(data, '$.analysis_id')),
                    flagged_by NUMBER(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.flagged_by' RETURNING NUMBER)),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Очередь рассылки
            "broadcast_queue": """
                CREATE TABLE broadcast_queue (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    status VARCHAR2(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.status')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Конфигурация приложения
            "config": """
                CREATE TABLE config (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Промпты для LLM
            "prompts": """
                CREATE TABLE prompts (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    prompt_type VARCHAR2(50) GENERATED ALWAYS AS (JSON_VALUE(data, '$.type')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Торговые сигналы
            "trading_signals": """
                CREATE TABLE trading_signals (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    symbol VARCHAR2(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.symbol')),
                    signal_type VARCHAR2(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.signal_type')),
                    confidence NUMBER GENERATED ALWAYS AS (JSON_VALUE(data, '$.confidence' RETURNING NUMBER)),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """,
            
            # Рыночные данные (OHLCV)
            "market_data": """
                CREATE TABLE market_data (
                    id VARCHAR2(50) PRIMARY KEY,
                    data CLOB CHECK (data IS JSON),
                    symbol VARCHAR2(20) GENERATED ALWAYS AS (JSON_VALUE(data, '$.symbol')),
                    interval_val VARCHAR2(10) GENERATED ALWAYS AS (JSON_VALUE(data, '$.interval')),
                    timestamp_val TIMESTAMP GENERATED ALWAYS AS (TO_TIMESTAMP(JSON_VALUE(data, '$.timestamp'), 'YYYY-MM-DD"T"HH24:MI:SS.FF"Z"')),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
        }
        
        cursor = self.connection.cursor()
        
        for table_name, create_sql in tables.items():
            try:
                logger.info(f"🔧 Создание таблицы {table_name}...")
                cursor.execute(create_sql)
                logger.info(f"✅ Таблица {table_name} создана успешно")
            except Exception as e:
                if "ORA-00955" in str(e):  # Table already exists
                    logger.info(f"ℹ️ Таблица {table_name} уже существует")
                else:
                    logger.error(f"❌ Ошибка создания таблицы {table_name}: {e}")
                    raise
        
        self.connection.commit()
        logger.info("✅ Все таблицы созданы успешно")
    
    def create_indexes(self):
        """Создание индексов для оптимизации производительности"""
        
        indexes = [
            # Индексы для пользователей
            "CREATE INDEX idx_users_telegram_id ON users (telegram_id)",
            "CREATE INDEX idx_users_username ON users (username)",
            "CREATE INDEX idx_users_role ON users (role)",
            "CREATE INDEX idx_users_tier ON users (tier)",
            
            # Индексы для подписок
            "CREATE INDEX idx_subscriptions_telegram_id ON subscriptions (telegram_id)",
            "CREATE INDEX idx_subscriptions_level ON subscriptions (level)",
            "CREATE INDEX idx_subscriptions_expires_at ON subscriptions (expires_at)",
            
            # Индексы для списков отслеживания
            "CREATE INDEX idx_watchlists_telegram_id ON watchlists (telegram_id)",
            
            # Индексы для банов
            "CREATE INDEX idx_bans_telegram_id ON bans (telegram_id)",
            "CREATE INDEX idx_bans_expires_at ON bans (expires_at)",
            
            # Индексы для анализов
            "CREATE INDEX idx_analyses_owner_id ON analyses (owner_id)",
            "CREATE INDEX idx_analyses_symbol ON analyses (symbol)",
            "CREATE INDEX idx_analyses_created_at ON analyses (created_at)",
            
            # Индексы для флагов
            "CREATE INDEX idx_flags_analysis_id ON flags (analysis_id)",
            "CREATE INDEX idx_flags_flagged_by ON flags (flagged_by)",
            
            # Индексы для очереди рассылки
            "CREATE INDEX idx_broadcast_queue_status ON broadcast_queue (status)",
            "CREATE INDEX idx_broadcast_queue_created_at ON broadcast_queue (created_at)",
            
            # Индексы для промптов
            "CREATE INDEX idx_prompts_type ON prompts (prompt_type)",
            
            # Индексы для торговых сигналов
            "CREATE INDEX idx_trading_signals_symbol ON trading_signals (symbol)",
            "CREATE INDEX idx_trading_signals_type ON trading_signals (signal_type)",
            "CREATE INDEX idx_trading_signals_confidence ON trading_signals (confidence)",
            "CREATE INDEX idx_trading_signals_created_at ON trading_signals (created_at)",
            
            # Индексы для рыночных данных
            "CREATE INDEX idx_market_data_symbol ON market_data (symbol)",
            "CREATE INDEX idx_market_data_interval ON market_data (interval_val)",
            "CREATE INDEX idx_market_data_timestamp ON market_data (timestamp_val)",
            "CREATE INDEX idx_market_data_symbol_interval ON market_data (symbol, interval_val)"
        ]
        
        cursor = self.connection.cursor()
        
        for index_sql in indexes:
            try:
                logger.info(f"🔧 Создание индекса...")
                cursor.execute(index_sql)
                logger.info(f"✅ Индекс создан успешно")
            except Exception as e:
                if "ORA-00955" in str(e):  # Index already exists
                    logger.info(f"ℹ️ Индекс уже существует")
                else:
                    logger.warning(f"⚠️ Ошибка создания индекса: {e}")
        
        self.connection.commit()
        logger.info("✅ Все индексы созданы успешно")
    
    def insert_initial_data(self):
        """Вставка начальных данных"""
        
        cursor = self.connection.cursor()
        
        # Конфигурация по умолчанию
        default_config = {
            "app_name": "ChartGenius",
            "version": "2.0",
            "default_symbol": "BTCUSDT",
            "default_interval": "4h",
            "default_days": 15,
            "max_analyses_per_user": 100,
            "analysis_ttl_days": 30,
            "supported_symbols": ["BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"],
            "supported_intervals": ["1h", "4h", "1d", "1w"],
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }
        
        try:
            cursor.execute(
                "INSERT INTO config (id, data) VALUES (:id, :data)",
                {
                    "id": "default",
                    "data": str(default_config).replace("'", '"')
                }
            )
            logger.info("✅ Конфигурация по умолчанию добавлена")
        except Exception as e:
            if "ORA-00001" in str(e):  # Unique constraint violation
                logger.info("ℹ️ Конфигурация по умолчанию уже существует")
            else:
                logger.error(f"❌ Ошибка добавления конфигурации: {e}")
        
        self.connection.commit()
    
    def verify_schema(self):
        """Проверка созданной схемы"""
        
        cursor = self.connection.cursor()
        
        # Проверяем таблицы
        cursor.execute("""
            SELECT table_name, num_rows 
            FROM user_tables 
            WHERE table_name IN (
                'USERS', 'SUBSCRIPTIONS', 'WATCHLISTS', 'BANS', 'ANALYSES', 
                'FLAGS', 'BROADCAST_QUEUE', 'CONFIG', 'PROMPTS', 
                'TRADING_SIGNALS', 'MARKET_DATA'
            )
            ORDER BY table_name
        """)
        
        tables = cursor.fetchall()
        
        logger.info("📊 Созданные таблицы:")
        for table_name, num_rows in tables:
            logger.info(f"   {table_name}: {num_rows or 0} строк")
        
        # Проверяем индексы
        cursor.execute("""
            SELECT COUNT(*) as index_count
            FROM user_indexes 
            WHERE table_name IN (
                'USERS', 'SUBSCRIPTIONS', 'WATCHLISTS', 'BANS', 'ANALYSES', 
                'FLAGS', 'BROADCAST_QUEUE', 'CONFIG', 'PROMPTS', 
                'TRADING_SIGNALS', 'MARKET_DATA'
            )
        """)
        
        index_count = cursor.fetchone()[0]
        logger.info(f"📈 Создано индексов: {index_count}")
        
        logger.info("✅ Схема базы данных готова к использованию!")
    
    def close(self):
        """Закрытие соединения"""
        if self.connection:
            self.connection.close()
            logger.info("🔒 Соединение с базой данных закрыто")

def main():
    """Основная функция инициализации схемы"""
    
    logger.info("🚀 Начинаем инициализацию схемы Oracle AJD для ChartGenius")
    
    try:
        initializer = OracleSchemaInitializer()
        
        # Создаем таблицы
        initializer.create_tables()
        
        # Создаем индексы
        initializer.create_indexes()
        
        # Добавляем начальные данные
        initializer.insert_initial_data()
        
        # Проверяем схему
        initializer.verify_schema()
        
        # Закрываем соединение
        initializer.close()
        
        logger.info("🎉 Инициализация схемы завершена успешно!")
        
    except Exception as e:
        logger.error(f"💥 Критическая ошибка инициализации: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
