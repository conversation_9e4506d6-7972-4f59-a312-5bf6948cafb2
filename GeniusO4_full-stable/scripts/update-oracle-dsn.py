#!/usr/bin/env python3
"""
Скрипт для обновления Oracle DSN в .env файле после создания новой базы данных
"""

import os
import sys
import re
from pathlib import Path

def update_oracle_dsn(new_dsn):
    """
    Обновляет ORACLE_DSN в .env файле
    
    Args:
        new_dsn (str): Новая строка подключения Oracle DSN
    """
    
    # Путь к .env файлу
    env_file = Path(__file__).parent.parent / ".env"
    
    if not env_file.exists():
        print(f"❌ Файл .env не найден: {env_file}")
        return False
    
    try:
        # Читаем содержимое файла
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Паттерн для поиска строки ORACLE_DSN
        pattern = r'^ORACLE_DSN=.*$'
        replacement = f'ORACLE_DSN={new_dsn}'
        
        # Заменяем строку
        new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Проверяем, что замена произошла
        if new_content == content:
            print("❌ Строка ORACLE_DSN не найдена в .env файле")
            return False
        
        # Записываем обновленное содержимое
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ ORACLE_DSN обновлен в файле {env_file}")
        print(f"   Новое значение: {new_dsn}")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при обновлении .env файла: {e}")
        return False

def validate_dsn(dsn):
    """
    Проверяет формат DSN строки
    
    Args:
        dsn (str): Строка подключения для проверки
        
    Returns:
        bool: True если формат правильный
    """
    
    # Ожидаемый формат: adb.eu-frankfurt-1.oraclecloud.com:1522/[ID]_chartgenius3_high.adb.oraclecloud.com
    pattern = r'^adb\.eu-frankfurt-1\.oraclecloud\.com:1522\/[a-zA-Z0-9_]+_chartgenius3_high\.adb\.oraclecloud\.com$'
    
    if re.match(pattern, dsn):
        return True
    else:
        print("⚠️  Предупреждение: DSN не соответствует ожидаемому формату")
        print("   Ожидаемый формат: adb.eu-frankfurt-1.oraclecloud.com:1522/[ID]_chartgenius3_high.adb.oraclecloud.com")
        return False

def main():
    """Основная функция"""
    
    print("🔧 Oracle DSN Updater для ChartGenius")
    print("=" * 50)
    
    if len(sys.argv) != 2:
        print("Использование:")
        print(f"  python {sys.argv[0]} <NEW_DSN>")
        print()
        print("Пример:")
        print(f"  python {sys.argv[0]} 'adb.eu-frankfurt-1.oraclecloud.com:1522/g3abc123def456_chartgenius3_high.adb.oraclecloud.com'")
        sys.exit(1)
    
    new_dsn = sys.argv[1].strip()
    
    if not new_dsn:
        print("❌ DSN не может быть пустым")
        sys.exit(1)
    
    print(f"📝 Новый DSN: {new_dsn}")
    
    # Валидация формата
    validate_dsn(new_dsn)
    
    # Обновление файла
    if update_oracle_dsn(new_dsn):
        print()
        print("✅ DSN успешно обновлен!")
        print()
        print("Следующие шаги:")
        print("1. Протестировать подключение:")
        print("   python scripts/test-oracle-connection.py")
        print()
        print("2. Перезапустить backend:")
        print("   python backend/app.py")
        print()
        print("3. Проверить работу приложения в браузере")
    else:
        print("❌ Не удалось обновить DSN")
        sys.exit(1)

if __name__ == "__main__":
    main()
