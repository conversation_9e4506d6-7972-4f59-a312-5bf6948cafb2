# Создание нового Oracle Autonomous JSON Database

## Инструкции для создания нового AJD инстанса

### 1. Войти в Oracle Cloud Console
1. Открыть https://cloud.oracle.com
2. Войти с учетными данными: <PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com
3. Выбрать регион: Germany Central (Frankfurt)

### 2. Удалить старый инстанс (если есть)
1. Перейти в **Database** → **Autonomous Database**
2. Найти инстанс **ChartGenius2**
3. Нажать на три точки → **Terminate**
4. Подтвердить удаление

### 3. Создать новый инстанс
1. Нажать **Create Autonomous Database**
2. Заполнить параметры:

#### Basic Information
- **Compartment**: root (default)
- **Display name**: `ChartGenius v3.0 Database`
- **Database name**: `ChartGenius3`

#### Choose a workload type
- Выбрать: **JSON**

#### Choose a deployment type
- Выбрать: **Shared Infrastructure**

#### Configure the database
- **Choose database version**: **23ai** (latest)
- **OCPU count**: **1** (Always Free)
- **Storage (TB)**: **0.02** (20 GB - Always Free)
- **Auto Scaling**: **Отключить**

#### Create administrator credentials
- **Username**: `ADMIN` (нельзя изменить)
- **Password**: `ChartGenius2025!`
- **Confirm password**: `ChartGenius2025!`

#### Choose network access
- Выбрать: **Secure access from everywhere**

#### Choose a license type
- Выбрать: **License Included**

### 4. Создание и ожидание
1. Нажать **Create Autonomous Database**
2. Дождаться статуса **AVAILABLE** (5-10 минут)
3. Инстанс будет готов к использованию

### 5. Получить Connection String
1. После создания нажать на имя базы данных
2. Перейти на вкладку **DB Connection**
3. Скопировать **Connection String** для **HIGH** уровня
4. Формат будет: `adb.eu-frankfurt-1.oraclecloud.com:1522/[UNIQUE_ID]_chartgenius3_high.adb.oraclecloud.com`

### 6. Обновить конфигурацию проекта

#### Обновить .env файл
```env
# Oracle Autonomous JSON Database Configuration
ORACLE_USERNAME=ADMIN
ORACLE_PASSWORD=ChartGenius2025!
ORACLE_DSN=[СКОПИРОВАННЫЙ_CONNECTION_STRING]
ORACLE_POOL_MIN=2
ORACLE_POOL_MAX=10
ORACLE_POOL_INCREMENT=1
```

#### Пример нового DSN
```
ORACLE_DSN=adb.eu-frankfurt-1.oraclecloud.com:1522/g3abc123def456_chartgenius3_high.adb.oraclecloud.com
```

### 7. Тестирование подключения
```bash
cd D:\project
python GeniusO4_full-stable\scripts\test-oracle-connection.py
```

### 8. Включить Oracle клиент в коде
После успешного тестирования включить Oracle клиент обратно в `oracle_client.py`

## Новые учетные данные

### Для документации
- **Database Name**: ChartGenius3
- **Username**: ADMIN
- **Password**: ChartGenius2025!
- **Region**: eu-frankfurt-1
- **Workload**: JSON
- **Tier**: Always Free

### Безопасность
- Пароль содержит: заглавные буквы, цифры, спецсимволы
- Длина: 16 символов
- Соответствует требованиям Oracle Cloud

## После создания

### Проверить статус
1. В Oracle Cloud Console статус должен быть **AVAILABLE**
2. Зеленый индикатор рядом с именем базы данных
3. Connection strings доступны

### Тестирование функциональности
1. Подключение к базе данных
2. Создание коллекций
3. Вставка тестовых документов
4. Запросы к данным
5. Удаление тестовых данных

## Troubleshooting

### Если создание не удается
- Проверить лимиты Always Free (максимум 2 AJD)
- Убедиться, что выбран правильный регион
- Проверить, что старый инстанс полностью удален

### Если подключение не работает
- Проверить правильность connection string
- Убедиться, что пароль введен правильно
- Проверить статус инстанса в консоли
