#!/usr/bin/env python3
"""
Простой тест подключения к Oracle AJD
"""

import os
import oracledb
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

def test_simple_connection():
    """Тест простого подключения к Oracle AJD"""
    
    # Параметры подключения
    username = os.getenv("ORACLE_USERNAME")
    password = os.getenv("ORACLE_PASSWORD") 
    dsn = os.getenv("ORACLE_DSN")
    
    print("🔧 Параметры подключения:")
    print(f"   Username: {username}")
    print(f"   Password: {'*' * len(password) if password else 'None'}")
    print(f"   DSN: {dsn[:50]}..." if dsn and len(dsn) > 50 else f"   DSN: {dsn}")
    
    if not all([username, password, dsn]):
        print("❌ Не все параметры подключения заданы!")
        return False
    
    try:
        print("\n🔍 Попытка подключения...")
        
        # Простое подключение без дополнительных параметров
        connection = oracledb.connect(
            user=username,
            password=password,
            dsn=dsn
        )
        
        print("✅ Подключение успешно!")
        
        # Тест простого запроса
        with connection.cursor() as cursor:
            cursor.execute("SELECT 'Hello from Oracle AJD!' as message FROM dual")
            result = cursor.fetchone()
            print(f"📝 Результат запроса: {result[0]}")
        
        connection.close()
        print("✅ Соединение закрыто")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка подключения: {e}")
        print(f"   Тип ошибки: {type(e).__name__}")
        return False

if __name__ == "__main__":
    print("🚀 Простой тест подключения к Oracle AJD")
    print("=" * 50)
    
    success = test_simple_connection()
    
    if success:
        print("\n🎉 Тест прошел успешно!")
    else:
        print("\n💥 Тест не прошел")
