#!/usr/bin/env python3
"""
Тест подключения к Oracle AJD с TLS аутентификацией (без wallet)
"""

import os
import oracledb
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

def test_tls_connection():
    """Тест подключения к Oracle AJD с TLS аутентификацией (без wallet)"""
    
    # Параметры подключения
    username = os.getenv("ORACLE_USERNAME")
    password = os.getenv("ORACLE_PASSWORD")
    dsn = os.getenv("ORACLE_DSN")
    
    print("🔧 Параметры подключения с TLS (без wallet):")
    print(f"   Username: {username}")
    print(f"   Password: {'*' * len(password) if password else 'None'}")
    print(f"   DSN: {dsn[:50]}..." if dsn and len(dsn) > 50 else f"   DSN: {dsn}")
    
    if not all([username, password, dsn]):
        print("❌ Не все параметры подключения заданы!")
        return False
    
    try:
        print("\n🔍 Попытка подключения с TLS аутентификацией (без wallet)...")
        
        # Простое подключение без wallet - только TLS
        connection = oracledb.connect(
            user=username,
            password=password,
            dsn=dsn
        )
        
        print("✅ Подключение к Oracle AJD с TLS успешно!")
        
        # Тест простого запроса
        with connection.cursor() as cursor:
            cursor.execute("SELECT 'Hello from Oracle AJD with TLS!' as message FROM dual")
            result = cursor.fetchone()
            print(f"📝 Результат запроса: {result[0]}")
        
        # Тест создания коллекции
        with connection.cursor() as cursor:
            try:
                cursor.execute("SELECT COUNT(*) FROM user_tables WHERE table_name = 'CHARTGENIUS_DATA'")
                table_exists = cursor.fetchone()[0] > 0
                print(f"📊 Таблица CHARTGENIUS_DATA существует: {'✅' if table_exists else '❌'}")
            except Exception as e:
                print(f"⚠️ Ошибка проверки таблицы: {e}")
        
        connection.close()
        print("🔒 Соединение закрыто")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка подключения к Oracle AJD с TLS: {e}")
        print(f"   Тип ошибки: {type(e).__name__}")
        
        # Дополнительная диагностика
        if "DPY-6005" in str(e):
            print("   💡 Возможные причины DPY-6005:")
            print("      - База данных требует mTLS аутентификацию")
            print("      - Неверные параметры подключения")
            print("      - Проблемы с сетевым подключением")
        elif "DPY-4011" in str(e):
            print("   💡 Возможные причины DPY-4011:")
            print("      - Соединение закрыто сервером")
            print("      - База данных не поддерживает TLS без wallet")
            print("      - Таймаут подключения")
        
        return False

if __name__ == "__main__":
    success = test_tls_connection()
    exit(0 if success else 1)
