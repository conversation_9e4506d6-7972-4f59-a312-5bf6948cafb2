# ChartGenius - Полное руководство по визуализации анализа

## Обзор системы полного анализа

ChartGenius использует **полноценный авторский prompt.txt (439 строк)** с 26 секциями профессионального технического анализа. Каждый элемент содержит поле **"explanation"** для детального описания визуализации.

## Структура данных анализа

```json
{
  "primary_analysis": { /* Основной анализ */ },
  "confidence_in_trading_decisions": { /* Уверенность в решениях */ },
  "unfinished_zones": [ /* Незавершенные зоны */ ],
  "imbalances": [ /* Дисбалансы */ ],
  "support_resistance_levels": { /* Поддержка/сопротивление */ },
  "trend_lines": [ /* Трендовые линии */ ],
  "pivot_points": [ /* Пивот точки */ ],
  "fibonacci_analysis": [ /* Фибоначчи */ ],
  "elliott_wave_analysis": [ /* Волны Эллиотта */ ],
  "divergence_analysis": [ /* Дивергенции */ ],
  "structural_edge": [ /* Структурные преимущества */ ],
  "candlestick_patterns": [ /* Свечные паттерны */ ],
  "indicators_analysis": { /* Анализ индикаторов */ },
  "volume_analysis": { /* Объемный анализ */ },
  "indicator_correlations": [ /* Корреляции индикаторов */ ],
  "gap_analysis": [ /* Анализ гэпов */ ],
  "psychological_levels": [ /* Психологические уровни */ ],
  "fair_value_gaps": [ /* Справедливые гэпы */ ],
  "extended_ichimoku_analysis": { /* Расширенный Ишимоку */ },
  "volatility_by_intervals": [ /* Волатильность по интервалам */ ],
  "anomalous_candles": [ /* Аномальные свечи */ ],
  "price_prediction": { /* Прогноз цены */ },
  "risk_management": { /* Управление рисками */ },
  "feedback": { /* Обратная связь */ }
}
```

## 1. PRIMARY_ANALYSIS - Основной анализ

**Назначение**: Общий обзор рыночной ситуации
**Визуализация**: 
- Основная информационная панель
- Цветовые индикаторы тренда (зеленый/красный)
- Текстовое описание текущего состояния

**Поля данных**:
```json
{
  "trend": "bullish/bearish/sideways",
  "strength": "weak/moderate/strong",
  "explanation": "Детальное объяснение для отображения"
}
```

## 2. CONFIDENCE_IN_TRADING_DECISIONS - Уверенность в решениях

**Назначение**: Оценка надежности торговых сигналов
**Визуализация**:
- Прогресс-бар с процентами (0-100%)
- Цветовая градация (красный < 30%, желтый 30-70%, зеленый > 70%)
- Текстовое пояснение уровня уверенности

**Поля данных**:
```json
{
  "confidence_level": 85,
  "explanation": "Высокая уверенность основана на..."
}
```

## 3. UNFINISHED_ZONES - Незавершенные зоны

**Назначение**: Области с незавершенными ценовыми движениями
**Визуализация**:
- Прямоугольные зоны на графике
- Полупрозрачная заливка (обычно желтая/оранжевая)
- Подписи с описанием зоны

**Поля данных**:
```json
[
  {
    "date": "2025-01-07T12:00:00Z",
    "price_high": 109500,
    "price_low": 109200,
    "explanation": "Зона с высокой вероятностью возврата цены"
  }
]
```

## 4. IMBALANCES - Дисбалансы

**Назначение**: Области ценовых дисбалансов (гэпы, быстрые движения)
**Визуализация**:
- Вертикальные полосы или прямоугольники
- Контрастная заливка (обычно синяя/фиолетовая)
- Стрелки указывающие направление дисбаланса

**Поля данных**:
```json
[
  {
    "date": "2025-01-07T14:00:00Z",
    "price_high": 108800,
    "price_low": 108600,
    "type": "bullish/bearish",
    "explanation": "Дисбаланс требует заполнения"
  }
]
```

## 5. SUPPORT_RESISTANCE_LEVELS - Поддержка/Сопротивление

**Назначение**: Ключевые уровни поддержки и сопротивления
**Визуализация**:
- Горизонтальные линии на графике
- Поддержка: зеленые линии
- Сопротивление: красные линии
- Подписи с ценами и силой уровня

**Поля данных**:
```json
{
  "supports": [
    {
      "price": 107800,
      "strength": "strong/medium/weak",
      "explanation": "Многократно протестированный уровень"
    }
  ],
  "resistances": [
    {
      "price": 109700,
      "strength": "strong",
      "explanation": "Сильное сопротивление с историей отбоев"
    }
  ]
}
```

## 6. TREND_LINES - Трендовые линии

**Назначение**: Линии тренда для определения направления движения
**Визуализация**:
- Наклонные линии на графике
- Восходящий тренд: зеленые линии
- Нисходящий тренд: красные линии
- Пунктирные линии для проекций

**Поля данных**:
```json
[
  {
    "start_date": "2025-01-05T00:00:00Z",
    "start_price": 107000,
    "end_date": "2025-01-07T00:00:00Z", 
    "end_price": 108500,
    "type": "ascending/descending",
    "explanation": "Восходящая линия тренда поддерживает рост"
  }
]
```

## 7. PIVOT_POINTS - Пивот точки

**Назначение**: Ключевые точки разворота на основе предыдущих периодов
**Визуализация**:
- Горизонтальные пунктирные линии
- Центральная пивот точка: синяя линия
- Уровни поддержки S1, S2, S3: зеленые линии
- Уровни сопротивления R1, R2, R3: красные линии

**Поля данных**:
```json
[
  {
    "type": "pivot/S1/S2/S3/R1/R2/R3",
    "price": 108400,
    "explanation": "Центральная пивот точка дня"
  }
]
```

## 8. FIBONACCI_ANALYSIS - Анализ Фибоначчи

**Назначение**: Уровни коррекции и расширения Фибоначчи
**Визуализация**:
- Горизонтальные линии с процентными подписями
- Цветовая градация от 0% до 100%
- Ключевые уровни: 23.6%, 38.2%, 50%, 61.8%, 78.6%

**Поля данных**:
```json
[
  {
    "level": 61.8,
    "price": 108200,
    "type": "retracement/extension",
    "explanation": "Золотое сечение - сильный уровень поддержки"
  }
]
```

## 9. ELLIOTT_WAVE_ANALYSIS - Волны Эллиотта

**Назначение**: Анализ волновой структуры рынка
**Визуализация**:
- Волнообразные линии с нумерацией волн (1,2,3,4,5,A,B,C)
- Импульсные волны: сплошные линии
- Коррекционные волны: пунктирные линии
- Цветовое кодирование по степени волн

**Поля данных**:
```json
[
  {
    "wave_number": "3",
    "wave_type": "impulse/corrective",
    "start_date": "2025-01-06T00:00:00Z",
    "start_price": 107500,
    "end_date": "2025-01-07T00:00:00Z",
    "end_price": 109000,
    "explanation": "Третья волна - самая сильная в импульсе"
  }
]
```

## 10. DIVERGENCE_ANALYSIS - Анализ дивергенций

**Назначение**: Расхождения между ценой и индикаторами
**Визуализация**:
- Соединительные линии между пиками/впадинами цены и индикатора
- Бычья дивергенция: зеленые линии
- Медвежья дивергенция: красные линии
- Скрытая дивергенция: пунктирные линии

**Поля данных**:
```json
[
  {
    "type": "bullish/bearish/hidden_bullish/hidden_bearish",
    "indicator": "RSI/MACD/Stochastic",
    "price_points": [
      {"date": "2025-01-06T00:00:00Z", "price": 107000},
      {"date": "2025-01-07T00:00:00Z", "price": 108500}
    ],
    "explanation": "Бычья дивергенция сигнализирует о развороте"
  }
]
```

## 11. STRUCTURAL_EDGE - Структурные преимущества

**Назначение**: Структурные элементы рынка дающие торговое преимущество
**Визуализация**:
- Выделенные области на графике
- Специальные маркеры для точек входа/выхода
- Цветовое кодирование по типу преимущества

**Поля данных**:
```json
[
  {
    "type": "liquidity_grab/stop_hunt/institutional_order",
    "date": "2025-01-07T10:00:00Z",
    "price": 108300,
    "explanation": "Захват ликвидности перед движением вверх"
  }
]
```

## 12. CANDLESTICK_PATTERNS - Свечные паттерны

**Назначение**: Классические паттерны японских свечей
**Визуализация**:
- Выделение свечей в паттерне контуром
- Подписи с названием паттерна
- Стрелки указывающие направление сигнала

**Поля данных**:
```json
[
  {
    "pattern_name": "doji/hammer/engulfing/harami",
    "date": "2025-01-07T08:00:00Z",
    "price": 108500,
    "signal": "bullish/bearish/neutral",
    "explanation": "Молот на поддержке - сигнал разворота вверх"
  }
]
```

## 13. INDICATORS_ANALYSIS - Анализ индикаторов

**Назначение**: Комплексный анализ технических индикаторов
**Визуализация**:
- Панели индикаторов под основным графиком
- Цветовые зоны (перекупленность/перепроданность)
- Сигнальные линии и пересечения

**Поля данных**:
```json
{
  "RSI": {
    "value": 55.2,
    "signal": "bullish/bearish/neutral",
    "explanation": "RSI в нейтральной зоне с бычьим уклоном"
  },
  "MACD": {
    "macd": 249.5,
    "signal": 170.3,
    "histogram": 79.2,
    "explanation": "MACD выше сигнальной линии - бычий сигнал"
  }
}
```

## 14. VOLUME_ANALYSIS - Объемный анализ

**Назначение**: Анализ торговых объемов и их влияния на цену
**Визуализация**:
- Гистограмма объемов под графиком цены
- Цветовое кодирование (зеленый/красный по направлению)
- Выделение аномальных объемов

**Поля данных**:
```json
{
  "average_volume": 45000,
  "current_volume": 67000,
  "volume_trend": "increasing/decreasing",
  "significant_levels": [
    {
      "date": "2025-01-07T12:00:00Z",
      "volume": 120000,
      "explanation": "Всплеск объема подтверждает прорыв"
    }
  ]
}
```

## 15. INDICATOR_CORRELATIONS - Корреляции индикаторов

**Назначение**: Взаимосвязи между различными индикаторами
**Визуализация**:
- Корреляционная матрица в виде тепловой карты
- Графики синхронности сигналов
- Индикаторы силы корреляции

**Поля данных**:
```json
[
  {
    "indicator1": "RSI",
    "indicator2": "Stochastic",
    "correlation": 0.85,
    "explanation": "Высокая корреляция усиливает сигналы"
  }
]
```

## 16. GAP_ANALYSIS - Анализ гэпов

**Назначение**: Анализ ценовых разрывов и их заполнения
**Визуализация**:
- Выделенные области гэпов на графике
- Стрелки показывающие направление заполнения
- Процент заполнения гэпа

**Поля данных**:
```json
[
  {
    "gap_type": "common/breakaway/exhaustion/measuring",
    "start_price": 108000,
    "end_price": 108500,
    "date": "2025-01-07T09:00:00Z",
    "filled_percentage": 60,
    "explanation": "Гэп прорыва частично заполнен"
  }
]
```

## 17. PSYCHOLOGICAL_LEVELS - Психологические уровни

**Назначение**: Круглые числа и психологически важные уровни
**Визуализация**:
- Горизонтальные линии на круглых числах
- Специальные маркеры для ключевых уровней
- Индикация силы психологического воздействия

**Поля данных**:
```json
[
  {
    "price": 109000,
    "type": "round_number/historical_high/historical_low",
    "strength": "strong/medium/weak",
    "explanation": "Круглое число 109,000 - сильное сопротивление"
  }
]
```

## 18. FAIR_VALUE_GAPS - Справедливые гэпы

**Назначение**: Области справедливой стоимости требующие заполнения
**Визуализация**:
- Прямоугольные зоны с особой заливкой
- Градиентная окраска по вероятности заполнения
- Стрелки направления ожидаемого движения

**Поля данных**:
```json
[
  {
    "date": "2025-01-07T11:00:00Z",
    "price_high": 108700,
    "price_low": 108400,
    "probability": 0.75,
    "explanation": "Высокая вероятность возврата к справедливой цене"
  }
]
```
