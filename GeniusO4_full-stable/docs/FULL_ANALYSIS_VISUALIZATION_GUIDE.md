# ChartGenius - Полное руководство по визуализации анализа

## Обзор системы полного анализа

ChartGenius использует **полноценный авторский prompt.txt (439 строк)** с 26 секциями профессионального технического анализа. Каждый элемент содержит поле **"explanation"** для детального описания визуализации.

## Структура данных анализа

```json
{
  "primary_analysis": { /* Основной анализ */ },
  "confidence_in_trading_decisions": { /* Уверенность в решениях */ },
  "unfinished_zones": [ /* Незавершенные зоны */ ],
  "imbalances": [ /* Дисбалансы */ ],
  "support_resistance_levels": { /* Поддержка/сопротивление */ },
  "trend_lines": [ /* Трендовые линии */ ],
  "pivot_points": [ /* Пивот точки */ ],
  "fibonacci_analysis": [ /* Фибоначчи */ ],
  "elliott_wave_analysis": [ /* Волны Эллиотта */ ],
  "divergence_analysis": [ /* Дивергенции */ ],
  "structural_edge": [ /* Структурные преимущества */ ],
  "candlestick_patterns": [ /* Свечные паттерны */ ],
  "indicators_analysis": { /* Анализ индикаторов */ },
  "volume_analysis": { /* Объемный анализ */ },
  "indicator_correlations": [ /* Корреляции индикаторов */ ],
  "gap_analysis": [ /* Анализ гэпов */ ],
  "psychological_levels": [ /* Психологические уровни */ ],
  "fair_value_gaps": [ /* Справедливые гэпы */ ],
  "extended_ichimoku_analysis": { /* Расширенный Ишимоку */ },
  "volatility_by_intervals": [ /* Волатильность по интервалам */ ],
  "anomalous_candles": [ /* Аномальные свечи */ ],
  "price_prediction": { /* Прогноз цены */ },
  "risk_management": { /* Управление рисками */ },
  "feedback": { /* Обратная связь */ }
}
```

## 1. PRIMARY_ANALYSIS - Основной анализ

**Назначение**: Общий обзор рыночной ситуации
**Визуализация**: 
- Основная информационная панель
- Цветовые индикаторы тренда (зеленый/красный)
- Текстовое описание текущего состояния

**Поля данных**:
```json
{
  "trend": "bullish/bearish/sideways",
  "strength": "weak/moderate/strong",
  "explanation": "Детальное объяснение для отображения"
}
```

## 2. CONFIDENCE_IN_TRADING_DECISIONS - Уверенность в решениях

**Назначение**: Оценка надежности торговых сигналов
**Визуализация**:
- Прогресс-бар с процентами (0-100%)
- Цветовая градация (красный < 30%, желтый 30-70%, зеленый > 70%)
- Текстовое пояснение уровня уверенности

**Поля данных**:
```json
{
  "confidence_level": 85,
  "explanation": "Высокая уверенность основана на..."
}
```

## 3. UNFINISHED_ZONES - Незавершенные зоны

**Назначение**: Области с незавершенными ценовыми движениями
**Визуализация**:
- Прямоугольные зоны на графике
- Полупрозрачная заливка (обычно желтая/оранжевая)
- Подписи с описанием зоны

**Поля данных**:
```json
[
  {
    "date": "2025-01-07T12:00:00Z",
    "price_high": 109500,
    "price_low": 109200,
    "explanation": "Зона с высокой вероятностью возврата цены"
  }
]
```

## 4. IMBALANCES - Дисбалансы

**Назначение**: Области ценовых дисбалансов (гэпы, быстрые движения)
**Визуализация**:
- Вертикальные полосы или прямоугольники
- Контрастная заливка (обычно синяя/фиолетовая)
- Стрелки указывающие направление дисбаланса

**Поля данных**:
```json
[
  {
    "date": "2025-01-07T14:00:00Z",
    "price_high": 108800,
    "price_low": 108600,
    "type": "bullish/bearish",
    "explanation": "Дисбаланс требует заполнения"
  }
]
```

## 5. SUPPORT_RESISTANCE_LEVELS - Поддержка/Сопротивление

**Назначение**: Ключевые уровни поддержки и сопротивления
**Визуализация**:
- Горизонтальные линии на графике
- Поддержка: зеленые линии
- Сопротивление: красные линии
- Подписи с ценами и силой уровня

**Поля данных**:
```json
{
  "supports": [
    {
      "price": 107800,
      "strength": "strong/medium/weak",
      "explanation": "Многократно протестированный уровень"
    }
  ],
  "resistances": [
    {
      "price": 109700,
      "strength": "strong",
      "explanation": "Сильное сопротивление с историей отбоев"
    }
  ]
}
```

## 6. TREND_LINES - Трендовые линии

**Назначение**: Линии тренда для определения направления движения
**Визуализация**:
- Наклонные линии на графике
- Восходящий тренд: зеленые линии
- Нисходящий тренд: красные линии
- Пунктирные линии для проекций

**Поля данных**:
```json
[
  {
    "start_date": "2025-01-05T00:00:00Z",
    "start_price": 107000,
    "end_date": "2025-01-07T00:00:00Z", 
    "end_price": 108500,
    "type": "ascending/descending",
    "explanation": "Восходящая линия тренда поддерживает рост"
  }
]
```

## 7. PIVOT_POINTS - Пивот точки

**Назначение**: Ключевые точки разворота на основе предыдущих периодов
**Визуализация**:
- Горизонтальные пунктирные линии
- Центральная пивот точка: синяя линия
- Уровни поддержки S1, S2, S3: зеленые линии
- Уровни сопротивления R1, R2, R3: красные линии

**Поля данных**:
```json
[
  {
    "type": "pivot/S1/S2/S3/R1/R2/R3",
    "price": 108400,
    "explanation": "Центральная пивот точка дня"
  }
]
```

## 8. FIBONACCI_ANALYSIS - Анализ Фибоначчи

**Назначение**: Уровни коррекции и расширения Фибоначчи
**Визуализация**:
- Горизонтальные линии с процентными подписями
- Цветовая градация от 0% до 100%
- Ключевые уровни: 23.6%, 38.2%, 50%, 61.8%, 78.6%

**Поля данных**:
```json
[
  {
    "level": 61.8,
    "price": 108200,
    "type": "retracement/extension",
    "explanation": "Золотое сечение - сильный уровень поддержки"
  }
]
```

## 9. ELLIOTT_WAVE_ANALYSIS - Волны Эллиотта

**Назначение**: Анализ волновой структуры рынка
**Визуализация**:
- Волнообразные линии с нумерацией волн (1,2,3,4,5,A,B,C)
- Импульсные волны: сплошные линии
- Коррекционные волны: пунктирные линии
- Цветовое кодирование по степени волн

**Поля данных**:
```json
[
  {
    "wave_number": "3",
    "wave_type": "impulse/corrective",
    "start_date": "2025-01-06T00:00:00Z",
    "start_price": 107500,
    "end_date": "2025-01-07T00:00:00Z",
    "end_price": 109000,
    "explanation": "Третья волна - самая сильная в импульсе"
  }
]
```

## 10. DIVERGENCE_ANALYSIS - Анализ дивергенций

**Назначение**: Расхождения между ценой и индикаторами
**Визуализация**:
- Соединительные линии между пиками/впадинами цены и индикатора
- Бычья дивергенция: зеленые линии
- Медвежья дивергенция: красные линии
- Скрытая дивергенция: пунктирные линии

**Поля данных**:
```json
[
  {
    "type": "bullish/bearish/hidden_bullish/hidden_bearish",
    "indicator": "RSI/MACD/Stochastic",
    "price_points": [
      {"date": "2025-01-06T00:00:00Z", "price": 107000},
      {"date": "2025-01-07T00:00:00Z", "price": 108500}
    ],
    "explanation": "Бычья дивергенция сигнализирует о развороте"
  }
]
```

## 11. STRUCTURAL_EDGE - Структурные преимущества

**Назначение**: Структурные элементы рынка дающие торговое преимущество
**Визуализация**:
- Выделенные области на графике
- Специальные маркеры для точек входа/выхода
- Цветовое кодирование по типу преимущества

**Поля данных**:
```json
[
  {
    "type": "liquidity_grab/stop_hunt/institutional_order",
    "date": "2025-01-07T10:00:00Z",
    "price": 108300,
    "explanation": "Захват ликвидности перед движением вверх"
  }
]
```

## 12. CANDLESTICK_PATTERNS - Свечные паттерны

**Назначение**: Классические паттерны японских свечей
**Визуализация**:
- Выделение свечей в паттерне контуром
- Подписи с названием паттерна
- Стрелки указывающие направление сигнала

**Поля данных**:
```json
[
  {
    "pattern_name": "doji/hammer/engulfing/harami",
    "date": "2025-01-07T08:00:00Z",
    "price": 108500,
    "signal": "bullish/bearish/neutral",
    "explanation": "Молот на поддержке - сигнал разворота вверх"
  }
]
```

## 13. INDICATORS_ANALYSIS - Анализ индикаторов

**Назначение**: Комплексный анализ технических индикаторов
**Визуализация**:
- Панели индикаторов под основным графиком
- Цветовые зоны (перекупленность/перепроданность)
- Сигнальные линии и пересечения

**Поля данных**:
```json
{
  "RSI": {
    "value": 55.2,
    "signal": "bullish/bearish/neutral",
    "explanation": "RSI в нейтральной зоне с бычьим уклоном"
  },
  "MACD": {
    "macd": 249.5,
    "signal": 170.3,
    "histogram": 79.2,
    "explanation": "MACD выше сигнальной линии - бычий сигнал"
  }
}
```

## 14. VOLUME_ANALYSIS - Объемный анализ

**Назначение**: Анализ торговых объемов и их влияния на цену
**Визуализация**:
- Гистограмма объемов под графиком цены
- Цветовое кодирование (зеленый/красный по направлению)
- Выделение аномальных объемов

**Поля данных**:
```json
{
  "average_volume": 45000,
  "current_volume": 67000,
  "volume_trend": "increasing/decreasing",
  "significant_levels": [
    {
      "date": "2025-01-07T12:00:00Z",
      "volume": 120000,
      "explanation": "Всплеск объема подтверждает прорыв"
    }
  ]
}
```

## 15. INDICATOR_CORRELATIONS - Корреляции индикаторов

**Назначение**: Взаимосвязи между различными индикаторами
**Визуализация**:
- Корреляционная матрица в виде тепловой карты
- Графики синхронности сигналов
- Индикаторы силы корреляции

**Поля данных**:
```json
[
  {
    "indicator1": "RSI",
    "indicator2": "Stochastic",
    "correlation": 0.85,
    "explanation": "Высокая корреляция усиливает сигналы"
  }
]
```

## 16. GAP_ANALYSIS - Анализ гэпов

**Назначение**: Анализ ценовых разрывов и их заполнения
**Визуализация**:
- Выделенные области гэпов на графике
- Стрелки показывающие направление заполнения
- Процент заполнения гэпа

**Поля данных**:
```json
[
  {
    "gap_type": "common/breakaway/exhaustion/measuring",
    "start_price": 108000,
    "end_price": 108500,
    "date": "2025-01-07T09:00:00Z",
    "filled_percentage": 60,
    "explanation": "Гэп прорыва частично заполнен"
  }
]
```

## 17. PSYCHOLOGICAL_LEVELS - Психологические уровни

**Назначение**: Круглые числа и психологически важные уровни
**Визуализация**:
- Горизонтальные линии на круглых числах
- Специальные маркеры для ключевых уровней
- Индикация силы психологического воздействия

**Поля данных**:
```json
[
  {
    "price": 109000,
    "type": "round_number/historical_high/historical_low",
    "strength": "strong/medium/weak",
    "explanation": "Круглое число 109,000 - сильное сопротивление"
  }
]
```

## 18. FAIR_VALUE_GAPS - Справедливые гэпы

**Назначение**: Области справедливой стоимости требующие заполнения
**Визуализация**:
- Прямоугольные зоны с особой заливкой
- Градиентная окраска по вероятности заполнения
- Стрелки направления ожидаемого движения

**Поля данных**:
```json
[
  {
    "date": "2025-01-07T11:00:00Z",
    "price_high": 108700,
    "price_low": 108400,
    "probability": 0.75,
    "explanation": "Высокая вероятность возврата к справедливой цене"
  }
]
```

## 19. EXTENDED_ICHIMOKU_ANALYSIS - Расширенный анализ Ишимоку

**Назначение**: Комплексный анализ системы Ишимоку
**Визуализация**:
- Облако Ишимоку (Kumo) с градиентной заливкой
- Линии Tenkan, Kijun, Chikou Span
- Цветовое кодирование сигналов

**Поля данных**:
```json
{
  "tenkan_sen": 108200,
  "kijun_sen": 107800,
  "senkou_span_a": 108000,
  "senkou_span_b": 107500,
  "chikou_span": 108300,
  "cloud_color": "bullish/bearish",
  "explanation": "Цена выше облака - бычий тренд подтвержден"
}
```

## 20. VOLATILITY_BY_INTERVALS - Волатильность по интервалам

**Назначение**: Анализ волатильности на разных временных интервалах
**Визуализация**:
- Гистограмма волатильности по периодам
- Цветовая градация от низкой к высокой волатильности
- Сравнительные графики

**Поля данных**:
```json
[
  {
    "interval": "1h/4h/1d/1w",
    "volatility": 2.5,
    "average_volatility": 1.8,
    "explanation": "Повышенная волатильность на 4-часовом интервале"
  }
]
```

## 21. ANOMALOUS_CANDLES - Аномальные свечи

**Назначение**: Выявление необычных свечных формаций
**Визуализация**:
- Выделение аномальных свечей специальным контуром
- Индикаторы типа аномалии
- Всплывающие подсказки с объяснением

**Поля данных**:
```json
[
  {
    "date": "2025-01-07T13:00:00Z",
    "anomaly_type": "high_volume/long_wick/gap/doji",
    "significance": "high/medium/low",
    "explanation": "Длинная верхняя тень указывает на давление продавцов"
  }
]
```

## 22. PRICE_PREDICTION - Прогноз цены

**Назначение**: Прогнозирование будущих ценовых движений
**Визуализация**:
- Проекционные линии с доверительными интервалами
- Цветовая градация по вероятности сценариев
- Целевые уровни с процентами достижения

**Поля данных**:
```json
{
  "short_term": {
    "timeframe": "1-4 hours",
    "target_price": 109200,
    "probability": 0.65,
    "explanation": "Краткосрочный рост к сопротивлению"
  },
  "medium_term": {
    "timeframe": "1-3 days",
    "target_price": 110000,
    "probability": 0.45,
    "explanation": "Прорыв сопротивления при увеличении объемов"
  }
}
```

## 23. RISK_MANAGEMENT - Управление рисками

**Назначение**: Рекомендации по управлению рисками
**Визуализация**:
- Зоны стоп-лоссов и тейк-профитов на графике
- Индикаторы размера позиции
- Соотношение риск/прибыль

**Поля данных**:
```json
{
  "stop_loss": 107500,
  "take_profit": [109500, 110200],
  "position_size": "2% of capital",
  "risk_reward_ratio": 2.5,
  "explanation": "Консервативный подход с соотношением 1:2.5"
}
```

## 24. FEEDBACK - Обратная связь

**Назначение**: Оценка качества предыдущих прогнозов
**Визуализация**:
- Статистические панели точности
- Графики производительности
- Индикаторы надежности модели

**Поля данных**:
```json
{
  "accuracy": 0.78,
  "total_predictions": 150,
  "successful_predictions": 117,
  "explanation": "Модель показывает стабильную точность 78%"
}
```

## Торговые рекомендации (TRADING_RECOMMENDATION)

**КРИТИЧЕСКИ ВАЖНО**: Это основной элемент для пользователей
**Визуализация**:
- Крупная информационная панель с рекомендацией
- Цветовое кодирование: BUY (зеленый), SELL (красный), HOLD (желтый)
- Детальная информация о точках входа/выхода

**Поля данных**:
```json
{
  "action": "BUY/SELL/HOLD",
  "entry_price": 108500,
  "stop_loss": 107800,
  "take_profit": [109200, 109800],
  "confidence": 0.75,
  "timeframe": "4h",
  "explanation": "Рекомендация основана на прорыве сопротивления с подтверждением объемом"
}
```

## Техническая реализация

### Приоритет отображения элементов:
1. **TRADING_RECOMMENDATION** - главная панель
2. **SUPPORT_RESISTANCE_LEVELS** - ключевые уровни
3. **INDICATORS_ANALYSIS** - технические индикаторы
4. **PRICE_PREDICTION** - прогнозы
5. Остальные элементы по важности

### Цветовая схема:
- **Бычьи сигналы**: #10B981 (зеленый)
- **Медвежьи сигналы**: #EF4444 (красный)
- **Нейтральные**: #F59E0B (желтый)
- **Информационные**: #3B82F6 (синий)

### Обязательные поля для каждого элемента:
- `explanation` - детальное описание для пользователя
- `date` - временная метка (где применимо)
- `price` - ценовой уровень (где применимо)

**ВАЖНО**: Все элементы должны использовать поле `explanation` для создания понятных пользователю описаний на графике.
