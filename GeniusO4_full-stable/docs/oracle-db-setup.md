# Oracle Autonomous JSON Database Setup для ChartGenius

## Текущая конфигурация

### Oracle Cloud Account
- **Email**: <PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com
- **Tenancy**: dko<PERSON><PERSON>ov
- **Region**: Germany Central (Frankfurt) - eu-frankfurt-1
- **Tenancy OCID**: ocid1.tenancy.oc1..aaaaaaaasecthoxri52vvd5ynsocfynaprt4w32pgpqxdm4623iledc445pa
- **Object Storage Namespace**: frlbhzh75zbu

### Текущая база данных
- **Database Name**: ChartGenius2
- **Type**: Autonomous JSON Database (Always Free)
- **Username**: ADMIN
- **Password**: -QDC2xg!Ecj2s@h1
- **Connection String**: adb.eu-frankfurt-1.oraclecloud.com:1522/g2fbf778b2604d0_chartgenius2_high.adb.oraclecloud.com

## Проблемы с подключением

### Ошибки
- DPY-6005: cannot connect to database
- DPY-4011: the database or network closed the connection

### Возможные причины
1. **Инстанс остановлен** - AJD может быть в состоянии STOPPED
2. **Неправильная строка подключения** - DSN может быть устаревшим
3. **Проблемы с SSL** - Oracle AJD требует SSL подключения
4. **Сетевые ограничения** - Firewall или региональные ограничения
5. **Неправильные учетные данные** - Пароль мог быть изменен

## План решения

### Шаг 1: Проверка статуса инстанса
1. Войти в Oracle Cloud Console: https://cloud.oracle.com
2. Перейти в Database → Autonomous Database
3. Найти инстанс ChartGenius2
4. Проверить статус (AVAILABLE/STOPPED/TERMINATED)

### Шаг 2: Если инстанс остановлен
1. Нажать кнопку "Start"
2. Дождаться статуса AVAILABLE
3. Обновить connection string если изменился

### Шаг 3: Если инстанс недоступен или есть проблемы
1. Удалить текущий инстанс ChartGenius2
2. Создать новый AJD инстанс

## Создание нового Oracle AJD инстанса

### Параметры создания
```
Database Name: ChartGenius3
Display Name: ChartGenius v3.0 Database
Workload Type: JSON
Deployment Type: Shared Infrastructure
Database Version: 23ai (latest)
OCPU Count: 1 (Always Free)
Storage: 20 GB (Always Free)
Auto Scaling: Disabled
Username: ADMIN
Password: ChartGenius2025!
Confirm Password: ChartGenius2025!
Network Access: Secure access from everywhere
License Type: License Included
```

### После создания
1. Дождаться статуса AVAILABLE (5-10 минут)
2. Скопировать Connection Strings
3. Обновить .env файл

## Обновление конфигурации

### Новые переменные для .env
```env
# Oracle Autonomous JSON Database Configuration
ORACLE_USERNAME=ADMIN
ORACLE_PASSWORD=ChartGenius2025!
ORACLE_DSN=[NEW_CONNECTION_STRING_HIGH]
ORACLE_POOL_MIN=2
ORACLE_POOL_MAX=10
ORACLE_POOL_INCREMENT=1
```

### Connection String формат
```
adb.eu-frankfurt-1.oraclecloud.com:1522/[UNIQUE_ID]_chartgenius3_high.adb.oraclecloud.com
```

## Тестирование подключения

### Команда для тестирования
```bash
cd D:\project
python GeniusO4_full-stable\scripts\test-oracle-connection.py
```

### Ожидаемый результат
```
🎉 All Oracle AJD tests passed successfully!
Your Oracle Autonomous JSON Database is ready for ChartGenius v2.0!
```

## Troubleshooting

### Если подключение не работает
1. Проверить статус инстанса в Oracle Cloud Console
2. Убедиться, что используется правильный connection string
3. Проверить, что пароль правильный
4. Убедиться, что инстанс в регионе Frankfurt
5. Проверить сетевое подключение к Oracle Cloud

### Полезные ссылки
- Oracle Cloud Console: https://cloud.oracle.com
- Oracle AJD Documentation: https://docs.oracle.com/en/cloud/paas/autonomous-json-database/
- Always Free Resources: https://docs.oracle.com/en-us/iaas/Content/FreeTier/freetier_topic-Always_Free_Resources.htm

## Backup план
Если Oracle AJD продолжает вызывать проблемы, можно временно использовать:
1. SQLite для локальной разработки
2. PostgreSQL на Oracle Cloud VM
3. MongoDB Atlas (бесплатный tier)
