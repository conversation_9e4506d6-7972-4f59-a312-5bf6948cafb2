# ChartGenius - Руководство по визуализации элементов анализа

## Обзор

Данный документ описывает как каждый элемент технического анализа из полноценного `prompt.txt` должен быть отрисован на TradingView графиках. Каждый элемент содержит поле `explanation` для отображения при активации.

## 1. Основные уровни (Support & Resistance Levels)

### Структура данных:
```json
{
  "support_resistance_levels": {
    "supports": [
      {
        "level": 107900,
        "date": "2025-01-07 15:00:00",
        "explanation": "Уровень поддержки на основе множественных касаний",
        "ray_slope": "horizontal"
      }
    ],
    "resistances": [
      {
        "level": 109300,
        "date": "2025-01-07 16:00:00", 
        "explanation": "Уровень сопротивления подтвержденный объемами",
        "ray_slope": "horizontal"
      }
    ]
  }
}
```

### Отрисовка:
- **Тип**: Горизонтальные линии (лучи)
- **Цвет поддержки**: Зеленый (#00FF00)
- **Цвет сопротивления**: Красный (#FF0000)
- **Стиль**: Сплошная линия
- **Толщина**: 2px
- **Расширение**: Луч вправо от указанной даты
- **Подпись**: Показывать цену уровня
- **Tooltip**: Отображать `explanation` при наведении

## 2. Трендовые линии (Trend Lines)

### Структура данных:
```json
{
  "trend_lines": {
    "lines": [
      {
        "type": "восходящая",
        "start_point": {
          "date": "2025-01-06 12:00:00",
          "price": 107500
        },
        "end_point": {
          "date": "2025-01-07 18:00:00",
          "price": 108200
        },
        "slope_angle": "15 градусов",
        "explanation": "Восходящий тренд подтвержденный тремя касаниями"
      }
    ]
  }
}
```

### Отрисовка:
- **Тип**: Прямая линия между двумя точками
- **Цвет восходящей**: Зеленый (#00AA00)
- **Цвет нисходящей**: Красный (#AA0000)
- **Стиль**: Сплошная линия
- **Толщина**: 1.5px
- **Расширение**: Продлевать линию вправо
- **Tooltip**: Показывать тип тренда и `explanation`

## 3. Уровни Фибоначчи (Fibonacci Analysis)

### Структура данных:
```json
{
  "fibonacci_analysis": {
    "based_on_local_trend": {
      "levels": {
        "0%": 107800,
        "23.6%": 108100,
        "50%": 108500,
        "61.8%": 108700,
        "75%": 108850,
        "86.6%": 108950,
        "100%": 109200
      },
      "start_point": {
        "date": "2025-01-06 10:00:00",
        "price": 107800
      },
      "end_point": {
        "date": "2025-01-07 14:00:00",
        "price": 109200
      },
      "explanation": "Фибоначчи построен от локального минимума к максимуму"
    }
  }
}
```

### Отрисовка:
- **Тип**: Горизонтальные уровни с заливкой между ними
- **Цвета уровней**:
  - 0%: Синий (#0000FF)
  - 23.6%: Голубой (#00FFFF)
  - 50%: Желтый (#FFFF00)
  - 61.8%: Оранжевый (#FFA500) - **ключевой уровень**
  - 75%: Розовый (#FF69B4)
  - 86.6%: Фиолетовый (#800080)
  - 100%: Красный (#FF0000)
- **Заливка**: Полупрозрачная между уровнями (opacity: 0.1)
- **Подписи**: Показывать процент и цену
- **Tooltip**: Отображать `explanation`

## 4. Волны Эллиота (Elliott Wave Analysis)

### Структура данных:
```json
{
  "elliott_wave_analysis": {
    "waves": [
      {
        "wave_number": 1,
        "start_point": {
          "date": "2025-01-06 08:00:00",
          "price": 107500
        },
        "end_point": {
          "date": "2025-01-06 12:00:00",
          "price": 108200
        },
        "explanation": "Первая импульсная волна восходящего движения"
      }
    ]
  }
}
```

### Отрисовка:
- **Тип**: Линии с номерами волн
- **Цвет импульсных волн (1,3,5)**: Зеленый (#00AA00)
- **Цвет коррекционных волн (2,4)**: Красный (#AA0000)
- **Цвет волн A,B,C**: Синий (#0000AA)
- **Стиль**: Пунктирная линия
- **Толщина**: 1px
- **Подписи**: Номер волны в кружке
- **Tooltip**: Показывать номер волны и `explanation`

## 5. Незавершенные зоны (Unfinished Zones)

### Структура данных:
```json
{
  "unfinished_zones": [
    {
      "type": "Bad High",
      "level": 109500,
      "date": "2025-01-07 16:00:00",
      "line_style": "dashed",
      "line_color": "orange",
      "explanation": "Слабый максимум требующий повторного тестирования"
    }
  ]
}
```

### Отрисовка:
- **Тип**: Горизонтальная линия с зоной
- **Цвета по типам**:
  - Bad High/Low: Оранжевый (#FFA500)
  - Weak High/Low: Желтый (#FFFF00)
  - Poor High/Low: Розовый (#FF69B4)
  - Sweep Levels: Фиолетовый (#800080)
- **Стиль**: Согласно `line_style` (solid/dashed/dotted)
- **Зона**: Прямоугольник ±10 пунктов от уровня
- **Tooltip**: Показывать тип зоны и `explanation`

## 6. Дисбалансы (Imbalances)

### Структура данных:
```json
{
  "imbalances": [
    {
      "type": "Fair Value Gap",
      "start_point": {
        "date": "2025-01-07 10:00:00",
        "price": 108000
      },
      "end_point": {
        "date": "2025-01-07 14:00:00",
        "price": 108500
      },
      "price_range": [108000, 108500],
      "explanation": "Ценовой разрыв требующий заполнения"
    }
  ]
}
```

### Отрисовка:
- **Тип**: Прямоугольная зона
- **Цвета по типам**:
  - Fair Value Gap: Голубой с прозрачностью (#00FFFF, opacity: 0.3)
  - Single Prints: Желтый с прозрачностью (#FFFF00, opacity: 0.3)
  - Vector Candles: Фиолетовый с прозрачностью (#800080, opacity: 0.3)
- **Границы**: Пунктирная линия
- **Tooltip**: Показывать тип дисбаланса и `explanation`

## 7. Дивергенции (Divergence Analysis)

### Структура данных:
```json
{
  "divergence_analysis": [
    {
      "indicator": "RSI",
      "type": "bullish_divergence",
      "date": "2025-01-07 15:00:00",
      "explanation": "Бычья дивергенция RSI сигнализирует о развороте"
    }
  ]
}
```

### Отрисовка:
- **Тип**: Стрелка или значок на графике
- **Цвета**:
  - Бычья дивергенция: Зеленая стрелка вверх ↗
  - Медвежья дивергенция: Красная стрелка вниз ↘
- **Размер**: Средний значок
- **Позиция**: На свече указанной даты
- **Tooltip**: Показывать индикатор, тип и `explanation`

## 8. Свечные паттерны (Candlestick Patterns)

### Структура данных:
```json
{
  "candlestick_patterns": [
    {
      "date": "2025-01-07 16:00:00",
      "type": "Hammer",
      "price": 108200,
      "explanation": "Молот сигнализирует о возможном развороте вверх"
    }
  ]
}
```

### Отрисовка:
- **Тип**: Значок над/под свечой
- **Цвета**:
  - Бычьи паттерны: Зеленый значок ▲
  - Медвежьи паттерны: Красный значок ▼
  - Нейтральные: Желтый значок ♦
- **Позиция**: Над High или под Low свечи
- **Tooltip**: Показывать название паттерна и `explanation`

## 9. Прогноз цены (Price Prediction)

### Структура данных:
```json
{
  "price_prediction": {
    "virtual_candles": [
      {
        "date": "2025-01-07 20:00:00",
        "open": 108200,
        "high": 108500,
        "low": 108000,
        "close": 108300
      }
    ]
  }
}
```

### Отрисовка:
- **Тип**: Виртуальные свечи
- **Цвет**: Полупрозрачный серый (#808080, opacity: 0.5)
- **Стиль**: Пунктирный контур
- **Разделитель**: Вертикальная линия между реальными и виртуальными данными
- **Подпись**: "Прогноз" в начале виртуальных свечей

## 10. Торговые рекомендации (Trading Recommendations)

### Структура данных:
```json
{
  "recommendations": {
    "trading_strategies": [
      {
        "strategy": "Long позиция",
        "entry_point": {
          "Price": 108000,
          "Date": "2025-01-07 18:00:00"
        },
        "exit_point": {
          "Price": 109000,
          "Date": "2025-01-08 12:00:00"
        },
        "stop_loss": 107500,
        "take_profit": 109500
      }
    ]
  }
}
```

### Отрисовка:
- **Entry Point**: Зеленая стрелка вверх "BUY" ↗
- **Exit Point**: Синяя стрелка "EXIT" →
- **Stop Loss**: Красная горизонтальная линия "SL"
- **Take Profit**: Зеленая горизонтальная линия "TP"
- **Цвета**: Согласно типу сигнала
- **Tooltip**: Показывать детали стратегии

## Общие принципы отрисовки

### Приоритеты слоев (z-index):
1. Свечи OHLC (базовый слой)
2. Уровни поддержки/сопротивления
3. Трендовые линии
4. Фибоначчи уровни
5. Зоны и дисбалансы
6. Волны Эллиота
7. Паттерны и сигналы
8. Прогнозные данные (верхний слой)

### Интерактивность:
- Все элементы должны иметь tooltip с `explanation`
- Возможность включения/выключения каждого типа элементов
- Клик по элементу показывает детальную информацию
- Hover эффекты для лучшей видимости

### Адаптивность:
- Автоматическое масштабирование при зуме
- Оптимизация отображения на разных таймфреймах
- Скрытие перекрывающихся элементов при необходимости

### Производительность:
- Ленивая загрузка сложных элементов
- Кэширование расчетов для повторного использования
- Оптимизация рендеринга при большом количестве элементов

## 11. Пивотные точки (Pivot Points)

### Структура данных:
```json
{
  "pivot_points": {
    "pivot": 108500,
    "resistance_1": 109000,
    "resistance_2": 109500,
    "resistance_3": 110000,
    "support_1": 108000,
    "support_2": 107500,
    "support_3": 107000,
    "explanation": "Пивотные точки рассчитаны на основе предыдущего дня"
  }
}
```

### Отрисовка:
- **Тип**: Горизонтальные пунктирные линии
- **Цвет Pivot**: Синий (#0000FF)
- **Цвет Resistance**: Красный (#FF0000)
- **Цвет Support**: Зеленый (#00FF00)
- **Стиль**: Пунктирная линия
- **Подписи**: P, R1, R2, R3, S1, S2, S3

## 12. Структурные элементы (Structural Edge)

### Структура данных:
```json
{
  "structural_edge": [
    {
      "type": "Swing Fail",
      "date": "2025-01-07 14:00:00",
      "price": 108800,
      "explanation": "Неудачная попытка обновить максимум"
    }
  ]
}
```

### Отрисовка:
- **Тип**: Специальные маркеры
- **Swing Fail**: Красный треугольник ▼
- **Failed Auction**: Желтый ромб ♦
- **Позиция**: На указанной цене и дате
- **Tooltip**: Показывать тип и `explanation`

## 13. Анализ объемов (Volume Analysis)

### Структура данных:
```json
{
  "volume_analysis": {
    "significant_volume_changes": [
      {
        "date": "2025-01-07 15:00:00",
        "price": 108300,
        "volume": 15000,
        "explanation": "Аномально высокий объем на уровне сопротивления"
      }
    ]
  }
}
```

### Отрисовка:
- **Тип**: Цветовая индикация объемов
- **Высокий объем**: Ярко-зеленый столбец
- **Низкий объем**: Тусклый серый столбец
- **Аномальный объем**: Красный столбец с маркером
- **Tooltip**: Показывать значение объема и `explanation`

## 14. Анализ разрывов (Gap Analysis)

### Структура данных:
```json
{
  "gap_analysis": {
    "gaps": [
      {
        "date": "2025-01-07 09:00:00",
        "gap_type": "Breakaway Gap",
        "price_range": [108000, 108200],
        "explanation": "Разрыв прорыва подтверждает начало нового тренда"
      }
    ]
  }
}
```

### Отрисовка:
- **Тип**: Прямоугольная зона с особым стилем
- **Common Gap**: Серый (#808080)
- **Breakaway Gap**: Зеленый (#00AA00)
- **Runaway Gap**: Синий (#0000AA)
- **Exhaustion Gap**: Красный (#AA0000)
- **Стиль**: Заливка с прозрачностью 0.4
- **Границы**: Пунктирная линия

## 15. Психологические уровни (Psychological Levels)

### Структура данных:
```json
{
  "psychological_levels": {
    "levels": [
      {
        "level": 108000,
        "date": "2025-01-07 12:00:00",
        "type": "Support",
        "explanation": "Круглое число 108000 действует как поддержка"
      }
    ]
  }
}
```

### Отрисовка:
- **Тип**: Толстые горизонтальные линии
- **Цвет**: Фиолетовый (#800080)
- **Толщина**: 3px
- **Стиль**: Сплошная линия
- **Подпись**: Показывать круглое число
- **Особенность**: Выделять круглые числа (000, 500)

## 16. Расширенный анализ Ichimoku

### Структура данных:
```json
{
  "extended_ichimoku_analysis": {
    "conversion_base_line_cross": {
      "date": "2025-01-07 13:00:00",
      "signal": "Bullish Cross",
      "explanation": "Пересечение Tenkan-Sen и Kijun-Sen вверх"
    },
    "price_vs_cloud": {
      "position": "Above the Cloud",
      "explanation": "Цена выше облака - бычий сигнал"
    }
  }
}
```

### Отрисовка:
- **Облако**: Зеленое (бычье) или красное (медвежье)
- **Tenkan-Sen**: Красная линия
- **Kijun-Sen**: Синяя линия
- **Chikou Span**: Зеленая линия
- **Пересечения**: Стрелки в точках пересечения
- **Сигналы**: Цветовые индикаторы

## 17. Аномальные свечи (Anomalous Candles)

### Структура данных:
```json
{
  "anomalous_candles": [
    {
      "date": "2025-01-07 16:00:00",
      "type": "Long Upper Shadow",
      "price": 108500,
      "explanation": "Длинная верхняя тень указывает на давление продавцов"
    }
  ]
}
```

### Отрисовка:
- **Тип**: Выделение аномальных свечей
- **Цвет**: Яркая обводка вокруг свечи
- **Long Upper Shadow**: Красная обводка
- **Long Lower Shadow**: Зеленая обводка
- **Doji**: Желтая обводка
- **Marubozu**: Синяя обводка
- **Tooltip**: Показывать тип аномалии и `explanation`

## 18. Корреляции индикаторов (Indicator Correlations)

### Отрисовка:
- **Тип**: Дополнительная панель с графиками корреляций
- **MACD-RSI**: Линейный график корреляции
- **ATR-Volatility**: Столбчатый график
- **Цвета**: Градиент от красного к зеленому
- **Диапазон**: -1 до +1 для корреляций

## Технические требования к реализации

### TradingView Lightweight Charts API:
```javascript
// Пример добавления уровня поддержки
chart.addLineSeries({
  color: '#00FF00',
  lineWidth: 2,
  lineStyle: LineStyle.Solid,
  crosshairMarkerVisible: true,
  lastValueVisible: true,
  priceLineVisible: true
});

// Пример добавления зоны Фибоначчи
chart.addAreaSeries({
  topColor: 'rgba(255, 165, 0, 0.4)',
  bottomColor: 'rgba(255, 165, 0, 0.1)',
  lineColor: '#FFA500',
  lineWidth: 1
});
```

### Управление слоями:
- Использовать z-index для правильного наложения
- Группировать связанные элементы
- Предоставить API для включения/выключения слоев

### Оптимизация:
- Виртуализация для большого количества элементов
- Дебаунсинг для интерактивных элементов
- Кэширование вычислений для повторного использования
