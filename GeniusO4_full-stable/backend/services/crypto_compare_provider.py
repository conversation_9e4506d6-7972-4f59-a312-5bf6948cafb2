# backend/services/crypto_compare_provider.py

import os
import re
import time
import pandas as pd
import httpx
import numpy as np
from datetime import datetime, timedelta

# API key для CryptoCompare
API_KEY = os.getenv("CRYPTOCOMPARE_API_KEY", "")
# базовый URL для исторических данных
BASE_URL = "https://min-api.cryptocompare.com/data/v2/histo"

# Тикер по умолчанию
DEFAULT_SYMBOL = os.getenv("DEFAULT_SYMBOL", "BTCUSDT")
# Валюта по умолчанию для котируемой части
DEFAULT_QUOTE = os.getenv("DEFAULT_QUOTE", "USD")
# Флаг логирования
DEBUG = os.getenv("DEBUG_LOGGING", "false").lower() == "true"
DEV_LOG_DIR = os.path.join(os.getcwd(), "backend", "dev_logs")

def generate_realistic_ohlcv_data(symbol: str, interval: str, limit: int) -> pd.DataFrame:
    """
    Генерирует реалистичные OHLCV данные для тестирования
    """
    # Базовые цены для разных символов
    base_prices = {
        "BTCUSDT": 45000,
        "ETHUSDT": 2500,
        "ADAUSDT": 0.5,
        "SOLUSDT": 100,
        "DOTUSDT": 7,
        "LINKUSDT": 15
    }

    # Определяем базовую цену
    base_price = base_prices.get(symbol, base_prices["BTCUSDT"])

    # Определяем временной интервал
    interval_minutes = {
        "1m": 1, "5m": 5, "15m": 15,
        "1h": 60, "4h": 240, "1d": 1440
    }

    minutes = interval_minutes.get(interval, 240)

    # Генерируем временные метки
    end_time = datetime.now()
    start_time = end_time - timedelta(minutes=minutes * limit)

    timestamps = []
    current_time = start_time
    for i in range(limit):
        timestamps.append(current_time)
        current_time += timedelta(minutes=minutes)

    # Генерируем цены с реалистичными колебаниями
    np.random.seed(42)  # Для воспроизводимости

    prices = []
    current_price = base_price

    for i in range(limit):
        # Случайное изменение цены (от -2% до +2%)
        change_percent = np.random.normal(0, 0.01)  # Среднее 0, стандартное отклонение 1%
        price_change = current_price * change_percent
        current_price += price_change

        # Генерируем OHLC для свечи
        volatility = current_price * 0.005  # 0.5% волатильность внутри свечи

        open_price = current_price
        high_price = open_price + abs(np.random.normal(0, volatility))
        low_price = open_price - abs(np.random.normal(0, volatility))
        close_price = open_price + np.random.normal(0, volatility * 0.5)

        # Убеждаемся, что high >= max(open, close) и low <= min(open, close)
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)

        # Генерируем объем
        base_volume = 1000 + np.random.exponential(500)
        quote_volume = base_volume * close_price

        prices.append({
            "Open Time": timestamps[i],
            "Close Time": timestamps[i] + timedelta(minutes=minutes),
            "Open": round(open_price, 2),
            "High": round(high_price, 2),
            "Low": round(low_price, 2),
            "Close": round(close_price, 2),
            "Volume": round(base_volume, 2),
            "Quote Asset Volume": round(quote_volume, 2),
            "Number of Trades": int(np.random.poisson(100)),
            "Ignore": 0,
            "Taker Buy Base Asset Volume": round(base_volume * 0.6, 2),
            "Taker Buy Quote Asset Volume": round(quote_volume * 0.6, 2)
        })

        current_price = close_price

    return pd.DataFrame(prices)


async def fetch_ohlcv(symbol: str, interval: str, limit: int) -> pd.DataFrame:
    """
    Получает OHLCV из CryptoCompare или генерирует тестовые данные, возвращает DataFrame
    с колонками:
      Open Time, Close Time, Open, High, Low, Close, Volume, Quote Asset Volume
    """
    # Если нет API ключа, используем тестовые данные
    if not API_KEY:
        print(f"⚠️  CRYPTOCOMPARE_API_KEY не настроен, используем тестовые данные для {symbol}")
        return generate_realistic_ohlcv_data(symbol, interval, limit)

    # 1) Подготовка параметров для реального API
    pair = (symbol or "").strip().upper() or DEFAULT_SYMBOL
    base, quote = pair, DEFAULT_QUOTE

    if any(sep in pair for sep in ["-", "_", "/"]):
        parts = re.split(r"[-_/]", pair)
        if len(parts) >= 2:
            base, quote = parts[0], parts[1]
    else:
        known_quotes = [
            "USDT", "USDC", "BUSD", "BTC", "ETH", "BNB",
            "USD", "EUR", "TRY",
        ]
        for q in known_quotes:
            if pair.endswith(q) and len(pair) > len(q):
                base, quote = pair[:-len(q)], q
                break

    if base == pair:
        quote = DEFAULT_QUOTE

    period = {
        "1m": "minute", "5m": "minute", "15m": "minute",
        "1h": "hour", "4h": "hour", "1d": "day"
    }[interval]
    agg = int(interval[:-1]) if period == "minute" else 1

    url = f"{BASE_URL}{period}"
    params = {
        "fsym": base,
        "tsym": quote,
        "limit": limit,
        "aggregate": agg,
        "api_key": API_KEY
    }

    try:
        # 2) Запрос к API
        async with httpx.AsyncClient() as client:
            resp = await client.get(url, params=params, timeout=10)
        payload = resp.json().get("Data", {}).get("Data", [])

        # 3) Конвертация в DataFrame
        df = pd.DataFrame(payload)
        if df.empty:
            print(f"⚠️  Нет данных от CryptoCompare для {symbol}, используем тестовые данные")
            return generate_realistic_ohlcv_data(symbol, interval, limit)

        # 4) Отладочное логирование сырого DataFrame
        if DEBUG:
            os.makedirs(DEV_LOG_DIR, exist_ok=True)
            ts = int(time.time())
            cols_file = os.path.join(DEV_LOG_DIR, f"raw_cols_{base}_{quote}_{interval}_{ts}.txt")
            with open(cols_file, "w", encoding="utf-8") as f:
                f.write("Columns:\n" + "\n".join(df.columns) + "\n\n")
                f.write("First rows:\n" + df.head(5).to_string())

        # 5) Переименовываем основные поля
        df.rename(columns={
            "open": "Open",
            "high": "High",
            "low": "Low",
            "close": "Close",
            "volumefrom": "Volume",
            "volumeto": "Quote Asset Volume"
        }, inplace=True)

        # 6) Делаем временные колонки из исходного time
        df["Open Time"] = pd.to_datetime(df["time"], unit="s")
        df["Close Time"] = df["Open Time"] + pd.to_timedelta(agg, unit=period)
        # убираем уже ненужную колонку time
        df.drop(columns=["time"], inplace=True)

        return df

    except Exception as e:
        print(f"⚠️  Ошибка при получении данных от CryptoCompare: {e}")
        print(f"Используем тестовые данные для {symbol}")
        return generate_realistic_ohlcv_data(symbol, interval, limit)
