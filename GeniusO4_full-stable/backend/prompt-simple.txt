{{ ohlc_data | tojson | default([]) }}

Проанализируй предоставленные данные OHLC (Open, High, Low, Close) и технические индикаторы.

Верни анализ в формате JSON со следующими разделами:

{
  "primary_analysis": "Общая оценка тренда и состояния актива",
  "support_resistance_levels": {
    "support": [список уровней поддержки],
    "resistance": [список уровней сопротивления]
  },
  "indicators_analysis": {
    "RSI": "анализ RSI",
    "MACD": "анализ MACD",
    "volume": "анализ объемов"
  },
  "trading_recommendation": "Рекомендация для торговли (BUY/SELL/HOLD)",
  "confidence": "Уровень уверенности в анализе (1-10)"
}

Используй только предоставленные данные. Ответ должен быть валидным JSON без блоков кода.
