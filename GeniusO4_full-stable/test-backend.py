#!/usr/bin/env python3
"""
Тестовый backend для комплексного тестирования ChartGenius
Предоставляет полный набор endpoints для тестирования workflow
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from datetime import datetime, timedelta
import random

app = FastAPI(title="ChartGenius Test Backend", version="1.0.0")

# CORS настройки
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Генерация тестовых OHLC данных
def generate_ohlc_data(count=144):
    """Генерирует реалистичные OHLC данные для тестирования"""
    data = []
    base_price = 43000.0
    current_time = datetime.now()
    
    for i in range(count):
        # Генерируем реалистичные колебания цены
        price_change = random.uniform(-500, 500)
        base_price += price_change * 0.1
        
        open_price = base_price + random.uniform(-100, 100)
        high_price = open_price + random.uniform(0, 300)
        low_price = open_price - random.uniform(0, 300)
        close_price = open_price + random.uniform(-200, 200)
        volume = random.uniform(100, 1000)
        
        timestamp = current_time - timedelta(hours=4*i)
        
        data.append({
            "time": int(timestamp.timestamp()),
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": round(volume, 2)
        })
    
    return list(reversed(data))  # Сортируем по времени

# Тестовые данные анализа
DEMO_ANALYSIS = {
    "signal": "BUY",
    "confidence": 85,
    "recommendation": "Рекомендуется покупка",
    "stop_loss": 42000.00,
    "take_profit": 45000.00,
    "risk_level": "Средний",
    "indicators": {
        "RSI": 35.20,
        "MACD": 0.15,
        "BB_position": "lower",
        "EMA_20": 43150.00,
        "SMA_50": 43200.00
    },
    "support_resistance": {
        "support_levels": [42000, 41500, 41000],
        "resistance_levels": [44000, 44500, 45000]
    },
    "analysis_details": {
        "trend": "Восходящий",
        "momentum": "Положительный",
        "volatility": "Умеренная",
        "market_sentiment": "Бычий"
    }
}

@app.get("/")
async def root():
    """Корневой endpoint для проверки работы сервера"""
    return {
        "message": "ChartGenius Test Backend",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/api/analyze")
async def analyze_real_data(request_data: dict = None):
    """
    Основной endpoint для анализа - имитирует реальный API
    Возвращает полный набор данных для тестирования
    """
    try:
        # Генерируем свежие OHLC данные
        ohlc_data = generate_ohlc_data(144)
        
        # Возвращаем полный ответ
        response = {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "analysis": DEMO_ANALYSIS,
            "ohlc": ohlc_data,
            "indicators": ["RSI", "MACD", "BB", "EMA", "SMA"],
            "request_params": request_data or {},
            "processing_time": "1.2s"
        }
        
        return JSONResponse(response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка анализа: {str(e)}")

@app.post("/api/analyze-test")
async def analyze_test_data(request_data: dict = None):
    """
    Endpoint для тестирования с демо-данными
    Используется кнопкой "Test (API)"
    """
    try:
        # Генерируем тестовые данные
        ohlc_data = generate_ohlc_data(100)
        
        response = {
            "status": "test_success",
            "timestamp": datetime.now().isoformat(),
            "analysis": DEMO_ANALYSIS,
            "ohlc": ohlc_data,
            "indicators": ["RSI", "MACD", "BB"],
            "test_mode": True,
            "request_params": request_data or {}
        }
        
        return JSONResponse(response)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Ошибка тестового анализа: {str(e)}")

@app.get("/api/indicators")
async def get_indicators():
    """Получение списка доступных индикаторов"""
    return {
        "indicators": [
            {"name": "RSI", "description": "Relative Strength Index"},
            {"name": "MACD", "description": "Moving Average Convergence Divergence"},
            {"name": "BB", "description": "Bollinger Bands"},
            {"name": "EMA", "description": "Exponential Moving Average"},
            {"name": "SMA", "description": "Simple Moving Average"}
        ]
    }

@app.get("/api/status")
async def get_status():
    """Получение статуса системы"""
    return {
        "status": "operational",
        "services": {
            "analysis_engine": "online",
            "data_provider": "online",
            "indicators": "online"
        },
        "last_update": datetime.now().isoformat()
    }

if __name__ == "__main__":
    print("🚀 Запуск ChartGenius Test Backend...")
    print("📊 Доступные endpoints:")
    print("   - POST /api/analyze - Основной анализ")
    print("   - POST /api/analyze-test - Тестовый анализ")
    print("   - GET /api/indicators - Список индикаторов")
    print("   - GET /api/status - Статус системы")
    print("   - GET /health - Health check")
    
    uvicorn.run(
        app, 
        host="127.0.0.1", 
        port=8000,
        log_level="info",
        reload=False
    )
