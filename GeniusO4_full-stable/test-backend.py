#!/usr/bin/env python3
"""
Простой тестовый backend для проверки демо-данных ChartGenius
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

app = FastAPI(title="ChartGenius Test Backend")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Тестовые данные для демонстрации
DEMO_ANALYSIS = {
    "primary_analysis": {
        "global_trend": "Восходящий тренд с коррекцией",
        "local_trend": "Локальная коррекция в рамках восходящего тренда",
        "patterns": "Формирование флага после импульсного роста",
        "anomalies": "Повышенные объемы на уровнях поддержки"
    },
    "confidence_in_trading_decisions": {
        "confidence": "high",
        "reason": "Четкие технические сигналы и подтверждение объемами"
    },
    "trading_recommendations": [
        {
            "action": "BUY",
            "entry_price": 105500,
            "stop_loss": 104800,
            "take_profit": 106800,
            "confidence": "high",
            "reasoning": "Отскок от уровня поддержки с подтверждением объемами"
        }
    ],
    "support_resistance": {
        "support_levels": [104800, 105200],
        "resistance_levels": [106000, 106800],
        "key_level": 105500
    }
}

DEMO_OHLC = [
    {"time": "2024-01-01", "open": 104000, "high": 104500, "low": 103800, "close": 104200},
    {"time": "2024-01-02", "open": 104200, "high": 104800, "low": 104000, "close": 104600},
    {"time": "2024-01-03", "open": 104600, "high": 105200, "low": 104400, "close": 105000},
    {"time": "2024-01-04", "open": 105000, "high": 105600, "low": 104800, "close": 105400},
    {"time": "2024-01-05", "open": 105400, "high": 105800, "low": 105100, "close": 105300},
    {"time": "2024-01-06", "open": 105300, "high": 105700, "low": 105000, "close": 105500},
    {"time": "2024-01-07", "open": 105500, "high": 106000, "low": 105200, "close": 105800},
    {"time": "2024-01-08", "open": 105800, "high": 106200, "low": 105600, "close": 105900},
    {"time": "2024-01-09", "open": 105900, "high": 106400, "low": 105700, "close": 106100},
    {"time": "2024-01-10", "open": 106100, "high": 106500, "low": 105900, "close": 106300}
]

@app.get("/api/health")
async def health_check():
    """Проверка здоровья API"""
    return {"status": "healthy", "message": "Test backend is running"}

@app.post("/api/analyze-test")
async def analyze_test(request_data: dict = None):
    """Тестовый endpoint для демо-данных"""
    return JSONResponse({
        "analysis": DEMO_ANALYSIS,
        "ohlc": DEMO_OHLC,
        "indicators": ["RSI", "MACD", "BB", "EMA", "SMA"]
    })

@app.post("/api/analyze")
async def analyze_real():
    """Реальный endpoint (возвращает ошибку для тестирования)"""
    return JSONResponse(
        status_code=503,
        content={"detail": "Real API temporarily unavailable for testing"}
    )

if __name__ == "__main__":
    print("🚀 Запуск тестового backend для ChartGenius...")
    print("📊 Доступные endpoints:")
    print("   GET  /api/health - проверка здоровья")
    print("   POST /api/analyze-test - демо-данные")
    print("   POST /api/analyze - реальные данные (недоступно)")
    print("🌐 Frontend: http://localhost:5173")
    print("🔧 Backend:  http://localhost:8000")
    
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
