import React, { useState } from 'react';
import { Settings, BarChart3, Layers } from 'lucide-react';
import TechnicalIndicators from '../TechnicalIndicators';
import AdvancedIndicators from '../AdvancedIndicators';
import ModelAnalysisIndicators from '../ModelAnalysisIndicators';
import { CollapsiblePanel } from './CollapsiblePanel';

const ControlPanel = ({
  symbol,
  setSymbol,
  limit,
  setLimit,
  interval,
  setInterval,
  loading,
  loadData,
  loadTestData,
  layers,
  toggleLayer,
  available,
  setLoading,
  setAnalysis,
  setData,
  setAvailable,
  analysis, // Добавляем analysis для умного управления пространством
  overlayControls // Добавляем overlayControls для новой вкладки "Наложения"
}) => {
  const [activeTab, setActiveTab] = useState('settings');
  const [isParametersCollapsed, setIsParametersCollapsed] = useState(false);

  // Автоматически сворачиваем параметры после успешного анализа
  React.useEffect(() => {
    if (analysis && Object.keys(analysis).length > 0) {
      setIsParametersCollapsed(true);
    }
  }, [analysis]);

  const handleTestAPI = async () => {
    try {
      setLoading(true);
      const body = { symbol, interval, limit, indicators: layers };
      const res = await fetch('/api/analyze-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });
      const json = await res.json();
      setAnalysis(json.analysis);
      setData(json.ohlc || []);
      setAvailable(json.indicators || []);
    } catch (error) {
      console.error('Ошибка тестового анализа:', error);
      // Заменяем alert на console.error
      console.error('Ошибка:', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="pro-panel">
      {/* Профессиональная навигация */}
      <nav className="pro-nav">
        <button
          onClick={() => setActiveTab('settings')}
          className={`pro-nav-item ${activeTab === 'settings' ? 'active' : ''}`}
        >
          <Settings size={16} className="mr-2" />
          Настройки
        </button>
        <button
          onClick={() => setActiveTab('indicators')}
          className={`pro-nav-item ${activeTab === 'indicators' ? 'active' : ''}`}
        >
          <BarChart3 size={16} className="mr-2" />
          Индикаторы
        </button>
        <button
          onClick={() => setActiveTab('overlays')}
          className={`pro-nav-item ${activeTab === 'overlays' ? 'active' : ''}`}
        >
          <Layers size={16} className="mr-2" />
          Наложения
        </button>
      </nav>

      {/* Содержимое вкладок */}
      <div className="p-4">
        {activeTab === 'settings' && (
          <div className="pro-space-y-4">


            <CollapsiblePanel
              title="Параметры запроса"
              icon={Settings}
              badge={analysis && Object.keys(analysis).length > 0 ? "Готов" : null}
              defaultOpen={!analysis || Object.keys(analysis).length === 0}
              persistKey="control-params"
              className="border-0 p-0 bg-transparent"
            >
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium pro-text-secondary mb-1">
                    Тикер
                  </label>
                  <input
                    type="text"
                    value={symbol}
                    onChange={(e) => setSymbol(e.target.value)}
                    className="pro-input w-full"
                    placeholder="BTCUSDT"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium pro-text-secondary mb-1">
                    Количество свечей
                  </label>
                  <input
                    type="number"
                    value={limit}
                    onChange={(e) => setLimit(+e.target.value)}
                    className="pro-input w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium pro-text-secondary mb-1">
                    Таймфрейм
                  </label>
                  <select
                    value={interval}
                    onChange={(e) => setInterval(e.target.value)}
                    className="pro-select w-full"
                  >
                    {['1m','5m','15m','1h','4h','1d'].map((tf) => (
                      <option key={tf} value={tf}>{tf}</option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2 pt-4">
                  <button
                    onClick={loadData}
                    disabled={loading}
                    className="pro-btn pro-btn-primary w-full"
                  >
                    {loading ? 'Анализ выполняется...' : 'Запустить анализ'}
                  </button>
                </div>
              </div>
            </CollapsiblePanel>
          </div>
        )}

        {activeTab === 'indicators' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold pro-text-primary mb-4">
                📊 Графические индикаторы
              </h3>
              <div className="grid grid-cols-1 gap-2">
                {['RSI','MACD','OBV','ATR','VWAP'].map((ind) => (
                  <label key={ind} className="flex items-center space-x-2 p-2 hover:pro-bg-hover rounded">
                    <input
                      type="checkbox"
                      checked={layers.includes(ind)}
                      onChange={() => toggleLayer(ind)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium pro-text-secondary">{ind}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold pro-text-primary mb-4">
                🔧 Технические индикаторы
              </h3>
              <TechnicalIndicators
                available={available}
                layers={layers}
                toggleLayer={toggleLayer}
              />
            </div>

            <div>
              <h3 className="text-lg font-semibold pro-text-primary mb-4">
                🚀 Продвинутые индикаторы
              </h3>
              <AdvancedIndicators
                available={available}
                layers={layers}
                toggleLayer={toggleLayer}
              />
            </div>

            <div>
              <h3 className="text-lg font-semibold pro-text-primary mb-4">
                🤖 Анализ модели
              </h3>
              <ModelAnalysisIndicators
                available={available}
                layers={layers}
                toggleLayer={toggleLayer}
              />
            </div>
          </div>
        )}

        {activeTab === 'overlays' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold pro-text-primary mb-4">
                🎯 Аналитические наложения
              </h3>
              <div className="pro-card p-4">
                <p className="text-sm pro-text-secondary mb-4">
                  Управление визуальными наложениями на график для технического анализа
                </p>
                {/* OverlayControls будет передан через props */}
                {overlayControls && overlayControls}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ControlPanel;
