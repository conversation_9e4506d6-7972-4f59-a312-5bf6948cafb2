import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import { User, Settings, BarChart3 } from 'lucide-react';
import TradingViewChart from '../TradingViewChart';
import ControlPanel from '../components/ControlPanel';
import ChartToolbar from '../components/ChartToolbar';
import CompactResults from '../components/CompactResults';
import OverlayControls from '../components/OverlayControls';
import QuickAccessPanel from '../components/QuickAccessPanel';
import RightPanel, { RightPanelSection } from '../components/RightPanel';
import TradingRecommendationsWidget from '../components/TradingRecommendationsWidget';

// Импорт реальных данных для тестирования
// Файл будет загружен динамически из public папки

// Простой компонент для отображения анализа
function AnalysisSections({ analysis, activeLayers = [] }) {
  if (!analysis) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>Нет данных анализа</p>
        <p className="text-sm mt-2">Запустите анализ для получения результатов</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">Результаты анализа</h3>

      {Object.entries(analysis).map(([key, value]) => {
        if (!value) return null;

        return (
          <div key={key} className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-2 capitalize">
              {key.replace(/_/g, ' ')}
            </h4>
            <div className="text-sm text-gray-600">
              {typeof value === 'string' ? (
                <p className="whitespace-pre-wrap">{value}</p>
              ) : typeof value === 'object' ? (
                <pre className="text-xs overflow-auto">
                  {JSON.stringify(value, null, 2)}
                </pre>
              ) : (
                <p>{String(value)}</p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}

function Home() {
  const token = useSelector((state) => state.auth.token);

  const [symbol,  setSymbol]  = useState('BTCUSDT');
  const [interval,setInterval]= useState('4h');
  const [limit,   setLimit]   = useState(144);

  const [realApiResponse, setRealApiResponse] = useState(null);
  const [layers,  setLayers]  = useState(['RSI']);
  const [data,    setData]    = useState([]);
  const [analysis,setAnalysis]= useState(null);
  const [overlaySettings, setOverlaySettings] = useState({
    showSupportResistance: true,
    showTrendLines: true,
    showFibonacci: true,
    showImbalances: true
  });
  const [available,setAvailable]= useState([]);
  const [loading, setLoading] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true); // Темная тема по умолчанию
  const [focusedSection, setFocusedSection] = useState(null);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);



  const toggleLayer = (name) =>
    setLayers((prev) =>
      prev.includes(name) ? prev.filter((l) => l !== name) : [...prev, name]);

  // Обработчик быстрых действий
  const handleQuickAction = (actionId) => {
    switch (actionId) {
      case 'view_recommendations':
        setFocusedSection('recommendations');
        // Прокрутка к секции рекомендаций
        document.querySelector('[data-section="recommendations"]')?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        break;
      case 'view_prediction':
        setFocusedSection('price_prediction');
        document.querySelector('[data-section="price_prediction"]')?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        break;
      case 'export_analysis':
        if (analysis) {
          const dataStr = JSON.stringify(analysis, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `analysis_${symbol}_${new Date().toISOString().split('T')[0]}.json`;
          link.click();
          URL.revokeObjectURL(url);
        }
        break;
      case 'share_analysis':
        if (analysis && navigator.share) {
          navigator.share({
            title: `Анализ ${symbol}`,
            text: `Торговый анализ для ${symbol}`,
            url: window.location.href
          });
        } else if (analysis) {
          // Fallback для браузеров без Web Share API
          navigator.clipboard.writeText(window.location.href);
        }
        break;
      default:
        // Неизвестное действие
    }
  };

  const handleSectionFocus = (sectionId) => {
    setFocusedSection(sectionId);
  };

  const loadTestData = async () => {
    try {
      // Демо-данные для тестирования визуальных эффектов
      const demoAnalysis = {
        recommendations: {
          action: "BUY",
          confidence: 0.85,
          reasons: [
            "Сильный восходящий тренд",
            "RSI показывает перепроданность",
            "Объем торгов увеличивается"
          ],
          target_price: 45000,
          stop_loss: 42000
        },
        price_prediction: {
          next_24h: 44500,
          next_7d: 46000,
          confidence: 0.78,
          trend: "bullish"
        },
        risk_assessment: {
          level: "medium",
          score: 0.6,
          factors: ["Высокая волатильность", "Макроэкономические риски"]
        },
        technical_indicators: {
          RSI: 35.2,
          MACD: 0.15,
          BB_position: "lower"
        }
      };

      // Демо-данные для графика (OHLC)
      const demoOhlcData = [
        { time: '2024-01-01', open: 103000, high: 103500, low: 102800, close: 103200 },
        { time: '2024-01-02', open: 103200, high: 103800, low: 103000, close: 103600 },
        { time: '2024-01-03', open: 103600, high: 104200, low: 103400, close: 103900 },
        { time: '2024-01-04', open: 103900, high: 104500, low: 103700, close: 104200 },
        { time: '2024-01-05', open: 104200, high: 104800, low: 104000, close: 104500 },
        { time: '2024-01-06', open: 104500, high: 105000, low: 104300, close: 104700 },
        { time: '2024-01-07', open: 104700, high: 105200, low: 104500, close: 104900 },
        { time: '2024-01-08', open: 104900, high: 105500, low: 104700, close: 105200 },
        { time: '2024-01-09', open: 105200, high: 105800, low: 105000, close: 105500 },
        { time: '2024-01-10', open: 105500, high: 106000, low: 105300, close: 105800 }
      ];

      setAnalysis(demoAnalysis);
      setData(demoOhlcData);
      setAvailable(['RSI', 'MACD', 'BB']);

      // Устанавливаем realApiResponse для демо-данных
      setRealApiResponse({
        analysis: demoAnalysis,
        ohlc: demoOhlcData,
        indicators: ['RSI', 'MACD', 'BB']
      });
    } catch (err) {
      console.error('Ошибка чтения тестовых данных:', err);
      // Заменяем alert на console.error
    }
  };

  const loadData = async () => {
    try {
      setLoading(true);
      // Запуск анализа

      const body = { symbol, interval, limit, indicators: layers };
      const headers = { 'Content-Type': 'application/json' };
      if (token) headers.Authorization = `Bearer ${token}`;

      const res = await fetch('/api/analyze', {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
      });

      // Ответ получен от API

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const json = await res.json();
      // Данные анализа получены

      setAnalysis(json.analysis);
      setData(json.ohlc || []);
      setAvailable(json.indicators || []);

      // Устанавливаем rawApiResponse для аналитических наложений
      setRealApiResponse(json);

      // Состояние обновлено
    } catch (error) {
      console.error('Ошибка при загрузке данных:', error);
      // Заменяем alert на console.error для избежания множественных диалогов
      console.error('Ошибка при выполнении анализа:', error.message);
      // Можно добавить toast уведомление или другой способ показа ошибки
    } finally {
      setLoading(false);
    }
  };

  // Автоматическая загрузка данных при запуске
  useEffect(() => {
    loadData();
  }, []); // Загружаем реальные данные при запуске

  return (
    <div className="pro-theme min-h-screen">
      {/* Трехколоночная структура: Левый Sidebar (20%) | График (60%) | Правая панель (20%) */}
      <div className="flex h-screen pro-layout-three-column lg:flex-row flex-col">
        {/* Левая панель - Панель управления (20%) */}
        <div className="lg:w-1/5 lg:min-w-[280px] w-full lg:order-1 order-1 pro-sidebar">
          <ControlPanel
            symbol={symbol}
            setSymbol={setSymbol}
            limit={limit}
            setLimit={setLimit}
            interval={interval}
            setInterval={setInterval}
            loading={loading}
            loadData={loadData}
            loadTestData={loadTestData}
            layers={layers}
            toggleLayer={toggleLayer}
            available={available}
            setLoading={setLoading}
            setAnalysis={setAnalysis}
            setData={setData}
            setAvailable={setAvailable}
            analysis={analysis}
            overlayControls={
              <OverlayControls
                overlaySettings={overlaySettings}
                onSettingsChange={setOverlaySettings}
              />
            }
          />


        </div>

        {/* Центральная область - График (60%) */}
        <div className={`flex-1 flex flex-col transition-all duration-300 lg:order-2 order-2 ${
          rightPanelCollapsed ? 'lg:w-4/5 w-full' : 'lg:w-3/5 w-full'
        }`}>
          {/* Панель инструментов графика */}
          <ChartToolbar
            symbol={symbol}
            interval={interval}
            onIntervalChange={setInterval}
            onFullscreen={() => {/* TODO: реализовать полноэкранный режим */}}
            isDarkMode={isDarkMode}
            onThemeToggle={() => setIsDarkMode(!isDarkMode)}
          />

          {/* Основной контент - График (ЦЕНТРАЛЬНЫЙ ЭЛЕМЕНТ) */}
          <div className="flex-1 p-4">
            <div className="pro-panel" style={{ height: '600px', minHeight: '400px' }}>
              <TradingViewChart
                data={data}
                layers={layers}
                analysis={analysis}
                rawApiResponse={realApiResponse}
                overlaySettings={overlaySettings}
              />
            </div>
          </div>
        </div>

        {/* Правая панель - Анализ и рекомендации (20%) */}
        <div className={`transition-all duration-300 lg:order-3 order-3 pro-right-panel-mobile ${
          rightPanelCollapsed ? 'lg:w-12 w-full' : 'lg:w-1/5 lg:min-w-[320px] w-full'
        }`}>
          <RightPanel
            collapsed={rightPanelCollapsed}
            onToggle={() => setRightPanelCollapsed(!rightPanelCollapsed)}
            rawApiResponse={realApiResponse}
            recommendations={analysis}
            isLoading={loading}
            className="h-full p-4"
          >
            {/* Быстрые метрики - ПРИОРИТЕТ 2 */}
            <RightPanelSection
              title="Быстрые метрики"
              icon={BarChart3}
              priority="medium"
              defaultOpen={true}
            >
              {analysis?.technical_indicators ? (
                <div className="space-y-2 text-xs">
                  {Object.entries(analysis.technical_indicators).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-pro-text-muted">{key}:</span>
                      <span className="text-pro-text-primary font-medium">
                        {typeof value === 'number' ? value.toFixed(2) : value}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-xs text-pro-text-secondary">
                  Метрики недоступны
                </div>
              )}
            </RightPanelSection>

            {/* Компактные результаты - ПРИОРИТЕТ 3 */}
            <RightPanelSection
              title="Детальный анализ"
              icon={Settings}
              priority="low"
              defaultOpen={false}
            >
              <CompactResults
                analysis={analysis}
                activeLayers={layers}
                onSectionFocus={handleSectionFocus}
                compact={true}
              />
            </RightPanelSection>
          </RightPanel>
        </div>
      </div>
    </div>
  );
}

export default Home;