import React, { useEffect, useRef, useState } from 'react';
import * as LightweightCharts from 'lightweight-charts';
import { createSeriesMarkers } from 'lightweight-charts';
import { toUnix, buildSeriesData, buildCandleData, validateTimeRange } from './utils/timeUtils';
import { indicatorColumnMap } from './indicatorGroups';
import { parseAnalysisData } from './utils/analysisDataParser';

export default function TradingViewChart({
  data = [],
  layers = [],
  analysis = {},
  rawApiResponse = null,
  overlaySettings = {
    showSupportResistance: true,
    showTrendLines: true,
    showFibonacci: true,
    showImbalances: true
  }
}) {
  const mainRef = useRef(null);
  const panelRef = useRef(null);
  const containerRef = useRef(null);
  const chartRef = useRef({});
  const [panelRatio, setPanelRatio] = useState(0.3);
  const [priceLines, setPriceLines] = useState([]);
  const [trendLineSeries, setTrendLineSeries] = useState([]);

  const panelIndicators = new Set([
    'RSI',
    'MACD',
    'OBV',
    'ATR',
    'ADX',
    'Stochastic_Oscillator',
    "Williams_%R",
    'Volume'
  ]);

  const colors = {
    MA_20: 'blue',
    MA_50: 'orange',
    MA_100: 'teal',
    MA_200: 'purple',
    RSI: '#ff9800',
    MACD: '#03a9f4',
    MACD_signal: '#e91e63',
    MACD_hist: '#9e9e9e',
    OBV: '#009688',
    ATR: '#795548',
    Stochastic_Oscillator: '#4caf50',
    "Williams_%R": '#ff5722',
    Volume: '#607d8b',
    Bollinger_Upper: '#2196f3',
    Bollinger_Middle: '#ffeb3b',
    Bollinger_Lower: '#2196f3',
    Ichimoku_A: '#4caf50',
    Ichimoku_B: '#f44336',
    Ichimoku_Base_Line: '#ff9800',
    Ichimoku_Conversion_Line: '#9c27b0',
    Moving_Average_Envelope_Upper: '#00bcd4',
    Moving_Average_Envelope_Lower: '#00bcd4',
    ADX: '#795548',
    EMA_12: '#e91e63',
    EMA_26: '#3f51b5',
    EMA_50: '#009688',
    EMA_100: '#ff5722',
    EMA_200: '#9c27b0',
    VWAP: '#607d8b',
    Parabolic_SAR: '#ff9800',
    CCI: '#4caf50',
    MFI: '#2196f3',
    ROC: '#9e9e9e',
    TSI: '#795548',
    Ultimate_Oscillator: '#ff5722',
    Aroon_Up: '#4caf50',
    Aroon_Down: '#f44336',
    TRIX: '#9c27b0',
    DPO: '#00bcd4',
    KST: '#ff9800',
    Chande_Momentum_Oscillator: '#607d8b',
    Klinger_Oscillator: '#e91e63',
    Price_Volume_Trend: '#3f51b5',
    Ease_of_Movement: '#009688',
    Negative_Volume_Index: '#ff5722',
    Positive_Volume_Index: '#9c27b0',
    Volume_Rate_of_Change: '#795548',
    Accumulation_Distribution_Line: '#4caf50',
    Chaikin_Money_Flow: '#2196f3',
    Force_Index: '#9e9e9e',
    Elder_Ray_Bull_Power: '#4caf50',
    Elder_Ray_Bear_Power: '#f44336',
    Mass_Index: '#ff9800',
    Vortex_Indicator_VI_Plus: '#4caf50',
    Vortex_Indicator_VI_Minus: '#f44336',
    Directional_Movement_Index: '#9c27b0',
    Plus_Directional_Indicator: '#4caf50',
    Minus_Directional_Indicator: '#f44336',
    Average_Directional_Index: '#ff9800',
    Commodity_Channel_Index: '#00bcd4',
    Detrended_Price_Oscillator: '#607d8b',
    Know_Sure_Thing: '#e91e63',
    Schaff_Trend_Cycle: '#3f51b5',
    Coppock_Curve: '#009688',
    Fibonacci_Pivot_Point_R3: '#ff5722',
    Fibonacci_Pivot_Point_R2: '#ff5722',
    Fibonacci_Pivot_Point_R1: '#ff5722',
    Fibonacci_Pivot_Point_PP: '#9c27b0',
    Fibonacci_Pivot_Point_S1: '#4caf50',
    Fibonacci_Pivot_Point_S2: '#4caf50',
    Fibonacci_Pivot_Point_S3: '#4caf50',
    Classic_Pivot_Point_R3: '#795548',
    Classic_Pivot_Point_R2: '#795548',
    Classic_Pivot_Point_R1: '#795548',
    Classic_Pivot_Point_PP: '#4caf50',
    Classic_Pivot_Point_S1: '#2196f3',
    Classic_Pivot_Point_S2: '#2196f3',
    Classic_Pivot_Point_S3: '#2196f3',
    Woodie_Pivot_Point_R2: '#9e9e9e',
    Woodie_Pivot_Point_R1: '#9e9e9e',
    Woodie_Pivot_Point_PP: '#ff9800',
    Woodie_Pivot_Point_S1: '#00bcd4',
    Woodie_Pivot_Point_S2: '#00bcd4',
    Camarilla_Pivot_Point_R4: '#607d8b',
    Camarilla_Pivot_Point_R3: '#607d8b',
    Camarilla_Pivot_Point_R2: '#607d8b',
    Camarilla_Pivot_Point_R1: '#607d8b',
    Camarilla_Pivot_Point_PP: '#e91e63',
    Camarilla_Pivot_Point_S1: '#3f51b5',
    Camarilla_Pivot_Point_S2: '#3f51b5',
    Camarilla_Pivot_Point_S3: '#3f51b5',
    Camarilla_Pivot_Point_S4: '#3f51b5',
    DM_Pivot_Point_R1: '#009688',
    DM_Pivot_Point_PP: '#ff5722',
    DM_Pivot_Point_S1: '#9c27b0'
  };

  // Функция для получения размеров контейнера
  const getContainerSize = () => {
    if (!containerRef.current) return { width: 800, height: 400 };
    const rect = containerRef.current.getBoundingClientRect();
    return {
      width: Math.max(rect.width || 800, 400),
      height: Math.max(rect.height || 400, 200)
    };
  };

  // Функция для добавления линий индикаторов
  const addLine = (chart, column, color) => {
    if (!data.length || !data[0][column]) return;
    
    const series = chart.addSeries(LightweightCharts.LineSeries, {
      color,
      lineWidth: 1,
      lastValueVisible: false,
      priceLineVisible: false
    });
    
    const seriesData = buildSeriesData(data, column);
    if (seriesData.length > 0) {
      series.setData(seriesData);
    }
  };

  // Основной useEffect для создания и обновления графиков
  useEffect(() => {
    console.log('=== TradingViewChart useEffect START ===');
    console.log('Data length:', data?.length);
    console.log('Layers:', layers);
    console.log('Container ref:', containerRef.current);
    console.log('Main ref:', mainRef.current);
    console.log('Panel ref:', panelRef.current);

    // Проверяем наличие данных и контейнеров
    if (!data?.length || !containerRef.current || !mainRef.current) {
      console.log('❌ Отсутствуют необходимые данные или контейнеры');
      return;
    }

    // Очищаем предыдущие графики
    Object.values(chartRef.current).forEach(chart => {
      if (chart && typeof chart.remove === 'function') {
        try {
          chart.remove();
        } catch (error) {
          console.warn('Ошибка при удалении графика:', error);
        }
      }
    });
    chartRef.current = {};

    try {
      // Получаем размеры контейнера
      const containerSize = getContainerSize();
      console.log('Container size:', containerSize);

      // Создаем основной график
      const mainChart = LightweightCharts.createChart(mainRef.current, {
        width: containerSize.width,
        height: Math.floor(containerSize.height * (1 - panelRatio)),
        layout: {
          background: { color: '#121212' },
          textColor: '#e0e0e0',
          fontSize: 12,
        },
        grid: {
          vertLines: { color: '#2a2a2a' },
          horzLines: { color: '#2a2a2a' },
        },
        handleScroll: {
          mouseWheel: true,
          pressedMouseMove: true,
        },
        handleScale: {
          axisPressedMouseMove: true,
          mouseWheel: true,
          pinch: true,
        },
        timeScale: {
          timeVisible: true,
          secondsVisible: false,
          borderColor: '#485158',
        },
        rightPriceScale: {
          borderColor: '#485158',
          scaleMargins: {
            top: 0.1,
            bottom: 0.1,
          },
        },
      });

      chartRef.current.main = mainChart;
      console.log('✅ Основной график создан');

      // Добавляем свечной график
      const candleSeries = mainChart.addSeries(LightweightCharts.CandlestickSeries, {
        lastValueVisible: true,
        priceLineVisible: false
      });

      // Определяем правильное поле времени и строим данные свечей
      const timeField = data[0]?.time !== undefined ? 'time' : 'Open Time';
      console.log('Используем поле времени:', timeField);
      
      const candleData = buildCandleData(data, timeField);
      console.log('Данные свечей построены, длина:', candleData.length);
      console.log('Первые 3 свечи:', candleData.slice(0, 3));

      if (candleData.length > 0) {
        candleSeries.setData(candleData);
        console.log('✅ Данные свечей установлены');
      }

      // Добавляем индикаторы на основной график
      const overlayLayers = layers.filter(l => !panelIndicators.has(l));
      console.log('Overlay layers:', overlayLayers);

      overlayLayers.forEach(layer => {
        const cols = indicatorColumnMap[layer] || [layer];
        if (!cols.some(c => data[0][c] !== undefined)) return;

        if (layer === 'Bollinger_Bands') {
          addLine(mainChart, 'Bollinger_Middle', colors.Bollinger_Middle);
          addLine(mainChart, 'Bollinger_Upper', colors.Bollinger_Upper);
          addLine(mainChart, 'Bollinger_Lower', colors.Bollinger_Lower);
        } else if (layer === 'Ichimoku_Cloud') {
          addLine(mainChart, 'Ichimoku_A', colors.Ichimoku_A);
          addLine(mainChart, 'Ichimoku_B', colors.Ichimoku_B);
          addLine(mainChart, 'Ichimoku_Base_Line', colors.Ichimoku_Base_Line);
          addLine(mainChart, 'Ichimoku_Conversion_Line', colors.Ichimoku_Conversion_Line);
        } else if (layer === 'Moving_Average_Envelopes') {
          addLine(mainChart, 'Moving_Average_Envelope_Upper', colors.Moving_Average_Envelope_Upper);
          addLine(mainChart, 'Moving_Average_Envelope_Lower', colors.Moving_Average_Envelope_Lower);
        } else {
          addLine(mainChart, layer, colors[layer] || 'white');
        }
      });

      // Создаем панельный график для индикаторов, если нужно
      const panelLayers = layers.filter(l => panelIndicators.has(l));
      console.log('Panel layers:', panelLayers);

      if (panelLayers.length > 0 && panelRef.current) {
        const panelChart = LightweightCharts.createChart(panelRef.current, {
          width: containerSize.width,
          height: Math.floor(containerSize.height * panelRatio),
          layout: {
            background: { color: '#121212' },
            textColor: '#e0e0e0',
            fontSize: 12,
          },
          grid: {
            vertLines: { color: '#2a2a2a' },
            horzLines: { color: '#2a2a2a' },
          },
          timeScale: {
            timeVisible: true,
            secondsVisible: false,
            borderColor: '#485158',
          },
          rightPriceScale: {
            borderColor: '#485158',
          },
        });

        chartRef.current.panel = panelChart;
        console.log('✅ Панельный график создан');

        // Добавляем индикаторы на панельный график
        panelLayers.forEach(layer => {
          addLine(panelChart, layer, colors[layer] || 'white');
        });

        // Синхронизируем временные шкалы
        mainChart.timeScale().subscribeVisibleTimeRangeChange(timeRange => {
          if (timeRange) {
            panelChart.timeScale().setVisibleTimeRange(timeRange);
          }
        });

        panelChart.timeScale().subscribeVisibleTimeRangeChange(timeRange => {
          if (timeRange) {
            mainChart.timeScale().setVisibleTimeRange(timeRange);
          }
        });
      }

      console.log('✅ График успешно создан и настроен');

    } catch (error) {
      console.error('❌ Ошибка при создании графика:', error);
    }

    console.log('=== TradingViewChart useEffect END ===');
  }, [data, layers, analysis, rawApiResponse, overlaySettings]);

  // Обработчик изменения размера окна
  useEffect(() => {
    const handleResize = () => {
      Object.values(chartRef.current).forEach((chart) => {
        if (chart && chart.resize) {
          const containerSize = getContainerSize();
          chart.resize(containerSize.width, containerSize.height);
        }
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [panelRatio]);

  const startDrag = (e) => {
    e.preventDefault();
    const startY = e.clientY;
    const startRatio = panelRatio;
    const height = containerRef.current?.getBoundingClientRect().height || 1;
    const onMove = (evt) => {
      const delta = evt.clientY - startY;
      let ratio = startRatio - delta / height;
      ratio = Math.min(0.9, Math.max(0.1, ratio));
      setPanelRatio(ratio);
    };
    const onUp = () => {
      window.removeEventListener('mousemove', onMove);
      window.removeEventListener('mouseup', onUp);
    };
    window.addEventListener('mousemove', onMove);
    window.addEventListener('mouseup', onUp);
  };

  const panelLayers = layers.filter((l) => panelIndicators.has(l));

  return (
    <div className="chart-panels" ref={containerRef}>
      <div className="legend">
        <span className="legend-item"><span className="legend-color" style={{background:'gray'}}></span>Свечи</span>
        {layers.map((l) => (
          <span key={l} className="legend-item"><span className="legend-color" style={{background:colors[l]||'black'}}></span>{l}</span>
        ))}
      </div>
      <div ref={mainRef} className="chart" style={{flexGrow: 1 - panelRatio}} />
      {panelLayers.length > 0 && (
        <>
          <div className="resize-handle" onMouseDown={startDrag} />
          <div ref={panelRef} className="chart-panel" style={{flexGrow: panelRatio}} />
        </>
      )}
    </div>
  );
}
