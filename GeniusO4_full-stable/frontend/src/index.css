@import "tailwindcss";
@import "./styles/modernEffects.css";
@import "./styles/professionalTheme.css";

@theme {
  /* Professional Trading Platform Dark Theme */
  --color-primary: #1f6feb;
  --color-primary-hover: #1a5cd8;
  --color-secondary: #8b949e;
  --color-success: #238636;
  --color-warning: #d29922;
  --color-error: #da3633;
  --color-background: #0d1117;
  --color-surface: #161b22;
  --color-text: #f0f6fc;
  --color-text-muted: #8b949e;
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
}

/* Professional Dark Theme Base Styles */
body {
  font-family: var(--font-family-sans);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
}

/* Apply professional theme to root */
#root {
  min-height: 100vh;
  background-color: var(--color-background);
}

/* TradingView Chart Styles */
.chart-panels {
  display: flex;
  flex-direction: column;
  height: 600px; /* Увеличиваем высоту */
  width: 100%;
  background: #121212;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.chart-panels .legend {
  display: flex;
  gap: 16px;
  padding: 8px 16px;
  background: #1a1a1a;
  border-bottom: 1px solid #333;
  font-size: 12px;
  color: #e0e0e0;
}

.chart-panels .legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.chart-panels .legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.chart-panels .chart {
  flex: 1;
  min-height: 300px;
  position: relative;
}

.chart-panels .chart-panel {
  flex: 0 0 auto;
  min-height: 150px;
  position: relative;
  border-top: 1px solid #333;
}

.chart-panels .resize-handle {
  height: 4px;
  background: #333;
  cursor: ns-resize;
  position: relative;
}

.chart-panels .resize-handle:hover {
  background: #555;
}

