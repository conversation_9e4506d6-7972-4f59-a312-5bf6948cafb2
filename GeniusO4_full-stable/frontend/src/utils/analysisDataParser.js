/**
 * Утилита для парсинга реальных данных анализа от модели
 * Обрабатывает JSON ответ и извлекает нужную информацию для компонентов
 */

/**
 * Парсит JSON ответ от ChatGPT API или прямые данные анализа
 * @param {Object} apiResponse - Полный ответ от API или прямые данные анализа
 * @returns {Object} Обработанные данные анализа
 */
export const parseAnalysisData = (apiResponse) => {
  try {
    let analysisData;

    // Проверяем, является ли это API ответом от ChatGPT или прямыми данными
    if (apiResponse?.choices?.[0]?.message?.content) {
      // Это API ответ от ChatGPT - извлекаем content
      const content = apiResponse.choices[0].message.content;
      analysisData = JSON.parse(content);
    } else if (apiResponse?.analysis) {
      // Это новый формат от test-backend
      analysisData = apiResponse.analysis;
    } else if (apiResponse?.primary_analysis || apiResponse?.recommendations) {
      // Это прямые данные анализа (старый формат)
      analysisData = apiResponse;
    } else {
      throw new Error('Неподдерживаемый формат данных');
    }
    
    // Обрабатываем новый формат от нашего бэкенда (trading_recommendation)
    if (analysisData.trading_recommendation && analysisData.confidence) {
      return {
        // Основной анализ из нашего формата
        primaryAnalysis: {
          signal: analysisData.trading_recommendation,
          confidence: analysisData.confidence,
          recommendation: analysisData.trading_recommendation,
          risk_level: 'medium'
        },

        // Уверенность в торговых решениях
        confidence: {
          overall: analysisData.confidence,
          signal: analysisData.trading_recommendation
        },

        // Уровни поддержки и сопротивления из нашего формата
        supportResistance: {
          supports: (analysisData.support_resistance_levels?.support || []).map(level => ({
            price: level,
            level: level,
            explanation: `Уровень поддержки ${level}`
          })),
          resistances: (analysisData.support_resistance_levels?.resistance || []).map(level => ({
            price: level,
            level: level,
            explanation: `Уровень сопротивления ${level}`
          }))
        },

        // Индикаторы из нашего формата
        indicatorsAnalysis: analysisData.indicators_analysis || {},

        // Торговые рекомендации из нашего формата
        recommendations: {
          trading_strategies: [{
            strategy: analysisData.trading_recommendation === 'BUY' ? 'Рекомендуется покупка' :
                     analysisData.trading_recommendation === 'SELL' ? 'Рекомендуется продажа' :
                     'Удержание позиции',
            signal_type: analysisData.trading_recommendation,
            confidence: analysisData.confidence / 10, // Конвертируем из 0-10 в 0-1
            risk: 'medium',
            entry_point: { Price: 0 },
            stop_loss: 0,
            take_profit: 0
          }]
        },

        // Детали анализа
        analysisDetails: {
          primary_analysis: analysisData.primary_analysis
        },

        // Остальные поля с пустыми значениями для совместимости
        trendLines: {},
        pivotPoints: {},
        unfinishedZones: [],
        imbalances: [],
        fibonacciAnalysis: {},
        elliottWaveAnalysis: {},
        divergenceAnalysis: [],
        structuralEdge: [],
        candlestickPatterns: [],
        volumeAnalysis: {},
        indicatorCorrelations: {},
        gapAnalysis: {},
        marketStructure: {},
        priceAction: {},
        timeFrameAnalysis: {},
        marketSentiment: {},
        riskManagement: {},
        tradingPlan: {}
      };
    }

    // Обрабатываем старый формат от test-backend
    if (analysisData.signal && analysisData.confidence) {
      return {
        // Основной анализ из нового формата
        primaryAnalysis: {
          signal: analysisData.signal,
          confidence: analysisData.confidence,
          recommendation: analysisData.recommendation,
          risk_level: analysisData.risk_level
        },

        // Уверенность в торговых решениях
        confidence: {
          overall: analysisData.confidence,
          signal: analysisData.signal
        },

        // Уровни поддержки и сопротивления из нового формата
        supportResistance: {
          supports: (analysisData.support_resistance?.support_levels || []).map(level => ({
            level: level,
            explanation: `Уровень поддержки ${level}`
          })),
          resistances: (analysisData.support_resistance?.resistance_levels || []).map(level => ({
            level: level,
            explanation: `Уровень сопротивления ${level}`
          }))
        },

        // Индикаторы из нового формата
        indicatorsAnalysis: {
          RSI: {
            current_value: analysisData.indicators?.RSI || 0,
            trend: analysisData.indicators?.RSI > 70 ? 'Перекупленность' :
                   analysisData.indicators?.RSI < 30 ? 'Перепроданность' : 'Нейтрально',
            comment: `RSI: ${analysisData.indicators?.RSI || 0}`
          },
          MACD: {
            current_value: analysisData.indicators?.MACD || 0,
            trend: analysisData.indicators?.MACD > 0 ? 'Бычий' : 'Медвежий',
            comment: `MACD: ${analysisData.indicators?.MACD || 0}`
          }
        },

        // Торговые рекомендации из нового формата
        recommendations: {
          trading_strategies: [{
            strategy: analysisData.recommendation || 'Нет рекомендации',
            entry_point: { Price: analysisData.take_profit || 0 },
            stop_loss: analysisData.stop_loss || 0,
            take_profit: analysisData.take_profit || 0,
            risk: analysisData.risk_level || 'Не указан'
          }]
        },

        // Детали анализа
        analysisDetails: analysisData.analysis_details || {},

        // Остальные поля с пустыми значениями для совместимости
        trendLines: {},
        pivotPoints: {},
        unfinishedZones: [],
        imbalances: [],
        fibonacciAnalysis: {},
        elliottWaveAnalysis: {},
        divergenceAnalysis: [],
        structuralEdge: [],
        candlestickPatterns: [],
        volumeAnalysis: {},
        indicatorCorrelations: {},
        gapAnalysis: {},
        psychologicalLevels: {},
        fairValueGaps: [],
        extendedIchimokuAnalysis: {},
        volatilityByIntervals: {},
        anomalousCandles: [],
        pricePrediction: {},
        feedback: {}
      };
    }

    // Старый формат данных
    return {
      // Основной анализ
      primaryAnalysis: analysisData.primary_analysis || {},

      // Уверенность в торговых решениях
      confidence: analysisData.confidence_in_trading_decisions || {},

      // Уровни поддержки и сопротивления
      supportResistance: analysisData.support_resistance_levels || {},

      // Трендовые линии
      trendLines: analysisData.trend_lines || {},

      // Пивот точки
      pivotPoints: analysisData.pivot_points || {},

      // Незавершенные зоны
      unfinishedZones: analysisData.unfinished_zones || [],

      // Дисбалансы
      imbalances: analysisData.imbalances || [],

      // Анализ Фибоначчи
      fibonacciAnalysis: analysisData.fibonacci_analysis || {},

      // Волновой анализ Эллиота
      elliottWaveAnalysis: analysisData.elliott_wave_analysis || {},

      // Анализ дивергенций
      divergenceAnalysis: analysisData.divergence_analysis || [],

      // Структурные преимущества
      structuralEdge: analysisData.structural_edge || [],

      // Свечные паттерны
      candlestickPatterns: analysisData.candlestick_patterns || [],

      // Анализ индикаторов
      indicatorsAnalysis: analysisData.indicators_analysis || {},

      // Анализ объемов
      volumeAnalysis: analysisData.volume_analysis || {},

      // Корреляции индикаторов
      indicatorCorrelations: analysisData.indicator_correlations || {},

      // Анализ гэпов
      gapAnalysis: analysisData.gap_analysis || {},

      // Психологические уровни
      psychologicalLevels: analysisData.psychological_levels || {},

      // Fair Value Gaps
      fairValueGaps: analysisData.fair_value_gaps || [],

      // Расширенный анализ Ичимоку
      extendedIchimokuAnalysis: analysisData.extended_ichimoku_analysis || {},

      // Волатильность по интервалам
      volatilityByIntervals: analysisData.volatility_by_intervals || {},

      // Аномальные свечи
      anomalousCandles: analysisData.anomalous_candles || [],

      // Прогноз цены
      pricePrediction: analysisData.price_prediction || {},

      // Торговые рекомендации
      recommendations: analysisData.recommendations || {},

      // Обратная связь модели
      feedback: analysisData.feedback || {}
    };
  } catch (error) {
    console.error('Ошибка парсинга данных анализа:', error);
    return null;
  }
};

/**
 * Извлекает торговые рекомендации в удобном формате
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Array} Массив торговых стратегий
 */
export const extractTradingRecommendations = (analysisData) => {
  if (!analysisData?.recommendations?.trading_strategies) {
    return [];
  }

  return analysisData.recommendations.trading_strategies.map((strategy, index) => ({
    id: index + 1,
    strategy: strategy.strategy || 'Не указана',
    signal: determineSignalType(strategy),
    entryPrice: strategy.entry_point?.Price || 0,
    entryDate: strategy.entry_point?.Date || '',
    exitPrice: strategy.exit_point?.Price || 0,
    exitDate: strategy.exit_point?.Date || '',
    stopLoss: strategy.stop_loss || 0,
    takeProfit: strategy.take_profit || 0,
    risk: strategy.risk || 'Не указан',
    profit: strategy.profit || 'Не указан',
    details: strategy.other_details || ''
  }));
};

/**
 * Определяет тип сигнала на основе стратегии
 * @param {Object} strategy - Торговая стратегия
 * @returns {string} Тип сигнала: 'BUY', 'SELL', 'HOLD'
 */
const determineSignalType = (strategy) => {
  const strategyText = (strategy.strategy || '').toLowerCase();
  
  if (strategyText.includes('нисходящ') || strategyText.includes('продаж') || strategyText.includes('короткой')) {
    return 'SELL';
  } else if (strategyText.includes('восходящ') || strategyText.includes('покупк') || strategyText.includes('длинной')) {
    return 'BUY';
  } else if (strategyText.includes('удержан') || strategyText.includes('коррекц')) {
    return 'HOLD';
  }
  
  return 'HOLD'; // По умолчанию
};

/**
 * Извлекает уровни поддержки и сопротивления для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Object} Уровни поддержки и сопротивления
 */
export const extractSupportResistanceLevels = (analysisData) => {
  const supportResistance = analysisData?.supportResistance || {};
  
  return {
    supports: (supportResistance.supports || []).map(level => ({
      price: level.level,
      date: level.date,
      explanation: level.explanation,
      type: 'support'
    })),
    resistances: (supportResistance.resistances || []).map(level => ({
      price: level.level,
      date: level.date,
      explanation: level.explanation,
      type: 'resistance'
    }))
  };
};

/**
 * Извлекает трендовые линии для отображения на графике
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Array} Массив трендовых линий
 */
export const extractTrendLines = (analysisData) => {
  const trendLines = analysisData?.trendLines?.lines || [];
  
  return trendLines.map((line, index) => ({
    id: index + 1,
    type: line.type,
    startPoint: {
      date: line.start_point?.date,
      price: line.start_point?.price
    },
    endPoint: {
      date: line.end_point?.date,
      price: line.end_point?.price
    },
    slopeAngle: line.slope_angle
  }));
};

/**
 * Извлекает уровни Фибоначчи
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Object} Уровни Фибоначчи
 */
export const extractFibonacciLevels = (analysisData) => {
  const fibonacci = analysisData?.fibonacciAnalysis || {};
  
  return {
    localTrend: {
      levels: fibonacci.based_on_local_trend?.levels || {},
      startPoint: fibonacci.based_on_local_trend?.start_point || {},
      endPoint: fibonacci.based_on_local_trend?.end_point || {},
      explanation: fibonacci.based_on_local_trend?.explanation || ''
    },
    globalTrend: {
      levels: fibonacci.based_on_global_trend?.levels || {},
      startPoint: fibonacci.based_on_global_trend?.start_point || {},
      endPoint: fibonacci.based_on_global_trend?.end_point || {},
      explanation: fibonacci.based_on_global_trend?.explanation || ''
    }
  };
};

/**
 * Извлекает ключевые индикаторы для отображения
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Object} Ключевые индикаторы
 */
export const extractKeyIndicators = (analysisData) => {
  const indicators = analysisData?.indicatorsAnalysis || {};
  
  return {
    rsi: {
      value: indicators.RSI?.current_value || 0,
      trend: indicators.RSI?.trend || '',
      comment: indicators.RSI?.comment || ''
    },
    macd: {
      value: indicators.MACD?.current_value || 0,
      signal: indicators.MACD?.signal || 0,
      histogram: indicators.MACD?.histogram || 0,
      trend: indicators.MACD?.trend || '',
      comment: indicators.MACD?.comment || ''
    },
    atr: {
      value: indicators.ATR?.current_value || 0,
      trend: indicators.ATR?.trend || '',
      comment: indicators.ATR?.comment || ''
    },
    adx: {
      value: indicators.ADX?.current_value || 0,
      trend: indicators.ADX?.trend || '',
      comment: indicators.ADX?.comment || ''
    }
  };
};

/**
 * Извлекает прогноз цены
 * @param {Object} analysisData - Обработанные данные анализа
 * @returns {Object} Прогноз цены
 */
export const extractPricePrediction = (analysisData) => {
  const prediction = analysisData?.pricePrediction || {};
  
  return {
    forecast: prediction.forecast || '',
    virtualCandles: prediction.virtual_candles || []
  };
};

/**
 * Форматирует цену для отображения
 * @param {number} price - Цена
 * @returns {string} Отформатированная цена
 */
export const formatPrice = (price) => {
  if (!price || isNaN(price)) return '0.00';
  return new Intl.NumberFormat('ru-RU', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(price);
};

/**
 * Форматирует дату для отображения
 * @param {string} dateString - Строка даты
 * @returns {string} Отформатированная дата
 */
export const formatDate = (dateString) => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return dateString;
  }
};
