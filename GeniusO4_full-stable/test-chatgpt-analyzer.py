#!/usr/bin/env python3
"""
Детальный тест ChatGPT анализатора для поиска проблем с парсингом
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Добавляем backend в путь
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

load_dotenv()

def test_chatgpt_analyzer():
    """Тестирует ChatGPT анализатор детально"""
    print("🧠 Детальное тестирование ChatGPT анализатора...")
    
    try:
        from services.chatgpt_analyzer import ChatGPTAnalyzer
        
        analyzer = ChatGPTAnalyzer()
        print("✅ ChatGPT анализатор инициализирован")
        
        # Тест 1: Проверяем extract_json
        print("\n🔍 Тест 1: extract_json")
        
        test_responses = [
            # Формат с блоком кода
            '```json\n{"test": "value"}\n```',
            # Формат без блока кода
            'Some text {"test": "value"} more text',
            # Пустой ответ
            'No JSON here',
            # Реальный ответ от OpenAI
            '''```json
{
  "BTC_Price": 109000,
  "Analysis": {
    "Current_Situation": "Bitcoin is currently at 109,000 USD.",
    "Market_Trend": "The price indicates a potential bullish trend"
  }
}
```'''
        ]
        
        for i, response in enumerate(test_responses):
            print(f"  Тест {i+1}: ", end="")
            json_str = analyzer.extract_json(response)
            if json_str:
                print(f"✅ Извлечен JSON: {json_str[:50]}...")
            else:
                print("❌ JSON не найден")
        
        # Тест 2: Проверяем construct_prompt
        print("\n🔍 Тест 2: construct_prompt")
        
        test_data = {
            "ohlc_summary": {"current_price": 109000, "change_24h": 2.5},
            "technical_indicators": {"RSI": 65.5},
            "volume_analysis": {"volume_trend": "increasing"}
        }
        
        prompt = analyzer.construct_prompt(test_data)
        if prompt:
            print(f"✅ Промпт создан, длина: {len(prompt)} символов")
            print(f"📝 Начало промпта: {prompt[:200]}...")
        else:
            print("❌ Промпт пустой")
        
        # Тест 3: Полный анализ с логированием
        print("\n🔍 Тест 3: Полный анализ")
        
        print("⏳ Выполняем полный анализ...")
        result = analyzer.analyze(test_data)
        
        if result:
            print(f"✅ Анализ успешен, ключи: {list(result.keys())}")
            for key, value in result.items():
                if isinstance(value, dict):
                    print(f"  {key}: {len(value)} подключей")
                else:
                    print(f"  {key}: {str(value)[:100]}...")
        else:
            print("❌ Анализ вернул пустой результат")
            
            # Проверим логи
            print("\n🔍 Проверяем логи...")
            log_dir = os.path.join(os.getcwd(), "backend", "dev_logs")
            if os.path.exists(log_dir):
                log_files = [f for f in os.listdir(log_dir) if f.startswith("analysis_")]
                if log_files:
                    latest_log = sorted(log_files)[-1]
                    print(f"📄 Последний лог: {latest_log}")
                    
                    with open(os.path.join(log_dir, latest_log), 'r', encoding='utf-8') as f:
                        log_content = f.read()
                        print(f"📝 Содержимое лога: {log_content[:500]}...")
                else:
                    print("📄 Логи анализа не найдены")
            else:
                print("📁 Директория логов не найдена")
        
    except Exception as e:
        print(f"💥 Ошибка: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chatgpt_analyzer()
