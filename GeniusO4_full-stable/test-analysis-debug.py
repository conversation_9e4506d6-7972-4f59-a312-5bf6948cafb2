#!/usr/bin/env python3
"""
Пошаговый тест анализа для поиска места зависания
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Добавляем backend в путь
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

load_dotenv()

async def test_analysis_step_by_step():
    """Тестирует каждый шаг анализа отдельно"""
    print("🔍 Пошаговое тестирование анализа...")
    
    # Шаг 1: Тест получения данных
    print("\n📊 Шаг 1: Получение OHLCV данных...")
    try:
        from services.crypto_compare_provider import fetch_ohlcv
        
        print("⏳ Получаем данные для BTCUSDT, 4h, 144 свечи...")
        df = await fetch_ohlcv("BTCUSDT", "4h", 144)
        
        if df.empty:
            print("❌ DataFrame пустой!")
            return
        else:
            print(f"✅ Получено {len(df)} строк данных")
            print(f"📋 Колонки: {list(df.columns)}")
            print(f"🕯️ Первая строка: {df.iloc[0].to_dict()}")
            
    except Exception as e:
        print(f"💥 Ошибка на шаге 1: {e}")
        return
    
    # Шаг 2: Тест обработки данных
    print("\n🔧 Шаг 2: Обработка данных...")
    try:
        from services.data_processor import DataProcessor

        processor = DataProcessor(df)  # Передаем DataFrame в конструктор
        print("⏳ Обрабатываем данные...")

        # Выполняем полную обработку
        processed_df = processor.perform_full_processing()

        print(f"✅ Данные обработаны, колонки: {list(processed_df.columns)}")

        # Получаем OHLC данные
        print("⏳ Получаем OHLC данные...")
        ohlc_data = processor.get_ohlc_data(144)

        print(f"✅ OHLC данные получены: {len(ohlc_data)} записей")

        # Создаем простые результаты анализа для следующего шага
        analysis_results = {
            "ohlc_data": ohlc_data,
            "processed_df": processed_df,
            "symbol": "BTCUSDT"
        }

    except Exception as e:
        print(f"💥 Ошибка на шаге 2: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Шаг 3: Тест LLM анализа (самый подозрительный)
    print("\n🤖 Шаг 3: LLM анализ...")
    try:
        from services.llm_service import LLMService
        
        llm_service = LLMService()
        print("✅ LLM сервис инициализирован")
        
        # Простой тест
        messages = [
            {"role": "system", "content": "You are a trading analyst."},
            {"role": "user", "content": "Analyze this: BTC price is 109000 USD. Give brief analysis in JSON format."}
        ]
        
        print("⏳ Отправляем запрос к LLM...")
        response = llm_service.generate(messages)
        
        print(f"✅ LLM ответ получен: {len(response.content)} символов")
        print(f"📝 Первые 200 символов: {response.content[:200]}...")
        
    except Exception as e:
        print(f"💥 Ошибка на шаге 3: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Шаг 4: Тест ChatGPT анализатора
    print("\n🧠 Шаг 4: ChatGPT анализатор...")
    try:
        from services.chatgpt_analyzer import ChatGPTAnalyzer
        
        analyzer = ChatGPTAnalyzer()
        print("✅ ChatGPT анализатор инициализирован")
        
        # Упрощенные данные для анализа
        simple_analysis = {
            "ohlc_summary": {"current_price": 109000, "change_24h": 2.5},
            "technical_indicators": {"RSI": 65.5},
            "volume_analysis": {"volume_trend": "increasing"}
        }
        
        print("⏳ Выполняем анализ через ChatGPT...")
        result = analyzer.analyze(simple_analysis)
        
        print(f"✅ Анализ завершен, ключи результата: {list(result.keys()) if result else 'Пустой результат'}")
        
    except Exception as e:
        print(f"💥 Ошибка на шаге 4: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 Все шаги выполнены успешно!")

if __name__ == "__main__":
    asyncio.run(test_analysis_step_by_step())
