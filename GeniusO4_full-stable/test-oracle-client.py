#!/usr/bin/env python3
"""
Тест Oracle клиента ChartGenius
Проверяет работу OracleJSONClient с новой схемой
"""

import os
import sys
import json
from datetime import datetime

# Добавляем путь к backend для импорта модулей
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.oracle_client import OracleJSONClient

def test_oracle_client():
    """Тестирование Oracle JSON клиента"""
    
    print("🚀 Тест Oracle JSON клиента ChartGenius")
    print("=" * 50)
    
    try:
        # Инициализация клиента
        print("🔧 Инициализация Oracle клиента...")
        client = OracleJSONClient()
        print("✅ Oracle клиент инициализирован успешно")
        
        # Тест 1: Создание пользователя
        print("\n📝 Тест 1: Создание пользователя")
        test_user = {
            "telegram_id": 299820674,
            "username": "admin_test",
            "first_name": "Admin",
            "last_name": "Test",
            "role": "admin",
            "tier": "premium",
            "lang": "ru",
            "tz": "+03:00",
            "created_at": datetime.utcnow().isoformat(),
            "last_seen": datetime.utcnow().isoformat()
        }
        
        success = client.insert_document("users", "test_admin", test_user)
        if success:
            print("✅ Пользователь создан успешно")
        else:
            print("❌ Ошибка создания пользователя")
        
        # Тест 2: Получение пользователя
        print("\n📖 Тест 2: Получение пользователя")
        user_data = client.get_document("users", "test_admin")
        if user_data:
            print(f"✅ Пользователь получен: {user_data.get('username', 'N/A')}")
            print(f"   Роль: {user_data.get('role', 'N/A')}")
            print(f"   Tier: {user_data.get('tier', 'N/A')}")
        else:
            print("❌ Пользователь не найден")
        
        # Тест 3: Обновление пользователя
        print("\n🔄 Тест 3: Обновление пользователя")
        updated_data = test_user.copy()
        updated_data["last_seen"] = datetime.utcnow().isoformat()
        updated_data["tier"] = "vip"
        
        success = client.update_document("users", "test_admin", updated_data)
        if success:
            print("✅ Пользователь обновлен успешно")
            
            # Проверяем обновление
            updated_user = client.get_document("users", "test_admin")
            if updated_user and updated_user.get("tier") == "vip":
                print("✅ Обновление подтверждено")
            else:
                print("❌ Обновление не применилось")
        else:
            print("❌ Ошибка обновления пользователя")
        
        # Тест 4: Создание анализа
        print("\n📊 Тест 4: Создание анализа")
        test_analysis = {
            "owner_id": 299820674,
            "symbol": "BTCUSDT",
            "interval": "4h",
            "analysis_result": {
                "recommendation": "BUY",
                "confidence": 0.85,
                "price_target": 45000,
                "stop_loss": 40000,
                "indicators": {
                    "rsi": 65.5,
                    "macd": "bullish",
                    "volume": "high"
                }
            },
            "created_at": datetime.utcnow().isoformat()
        }
        
        analysis_id = f"analysis_{int(datetime.utcnow().timestamp())}"
        success = client.insert_document("analyses", analysis_id, test_analysis)
        if success:
            print(f"✅ Анализ создан: {analysis_id}")
        else:
            print("❌ Ошибка создания анализа")
        
        # Тест 5: Создание торгового сигнала
        print("\n📈 Тест 5: Создание торгового сигнала")
        test_signal = {
            "symbol": "BTCUSDT",
            "signal_type": "BUY",
            "confidence": 0.85,
            "price": 43500.0,
            "target_price": 45000.0,
            "stop_loss": 40000.0,
            "timeframe": "4h",
            "indicators": ["RSI", "MACD", "Volume"],
            "created_at": datetime.utcnow().isoformat()
        }
        
        signal_id = f"signal_{int(datetime.utcnow().timestamp())}"
        success = client.insert_document("trading_signals", signal_id, test_signal)
        if success:
            print(f"✅ Торговый сигнал создан: {signal_id}")
        else:
            print("❌ Ошибка создания торгового сигнала")
        
        # Тест 6: Создание рыночных данных
        print("\n💹 Тест 6: Создание рыночных данных")
        test_market_data = {
            "symbol": "BTCUSDT",
            "interval": "4h",
            "timestamp": datetime.utcnow().isoformat(),
            "ohlcv": {
                "open": 43000.0,
                "high": 43800.0,
                "low": 42500.0,
                "close": 43500.0,
                "volume": 1250000.0
            },
            "indicators": {
                "sma_20": 42800.0,
                "ema_12": 43200.0,
                "rsi": 65.5,
                "macd": 150.0
            }
        }
        
        market_id = f"market_{int(datetime.utcnow().timestamp())}"
        success = client.insert_document("market_data", market_id, test_market_data)
        if success:
            print(f"✅ Рыночные данные созданы: {market_id}")
        else:
            print("❌ Ошибка создания рыночных данных")
        
        # Тест 7: Поиск документов
        print("\n🔍 Тест 7: Поиск документов")
        
        # Поиск пользователей по роли
        admin_users = client.find_documents("users", {"role": "admin"})
        print(f"✅ Найдено администраторов: {len(admin_users)}")
        
        # Поиск анализов по символу
        btc_analyses = client.find_documents("analyses", {"symbol": "BTCUSDT"})
        print(f"✅ Найдено анализов BTC: {len(btc_analyses)}")
        
        # Поиск торговых сигналов
        buy_signals = client.find_documents("trading_signals", {"signal_type": "BUY"})
        print(f"✅ Найдено сигналов BUY: {len(buy_signals)}")
        
        # Тест 8: Удаление тестовых данных
        print("\n🗑️ Тест 8: Очистка тестовых данных")
        
        # Удаляем тестового пользователя
        success = client.delete_document("users", "test_admin")
        if success:
            print("✅ Тестовый пользователь удален")
        else:
            print("❌ Ошибка удаления пользователя")
        
        # Удаляем тестовый анализ
        success = client.delete_document("analyses", analysis_id)
        if success:
            print("✅ Тестовый анализ удален")
        else:
            print("❌ Ошибка удаления анализа")
        
        # Удаляем тестовый сигнал
        success = client.delete_document("trading_signals", signal_id)
        if success:
            print("✅ Тестовый сигнал удален")
        else:
            print("❌ Ошибка удаления сигнала")
        
        # Удаляем тестовые рыночные данные
        success = client.delete_document("market_data", market_id)
        if success:
            print("✅ Тестовые рыночные данные удалены")
        else:
            print("❌ Ошибка удаления рыночных данных")
        
        print("\n🎉 Все тесты Oracle клиента завершены успешно!")
        return True
        
    except Exception as e:
        print(f"\n💥 Критическая ошибка тестирования: {e}")
        print(f"   Тип ошибки: {type(e).__name__}")
        return False

def test_connection_pool():
    """Тест пула соединений"""
    
    print("\n🏊 Тест пула соединений")
    print("-" * 30)
    
    try:
        client = OracleJSONClient()
        
        # Проверяем статус пула
        if hasattr(client, 'pool') and client.pool:
            print(f"✅ Пул соединений активен")
            print(f"   Открытых соединений: {client.pool.opened}")
            print(f"   Максимум соединений: {client.pool.max}")
            print(f"   Минимум соединений: {client.pool.min}")
        else:
            print("❌ Пул соединений не инициализирован")
            
        return True
        
    except Exception as e:
        print(f"❌ Ошибка тестирования пула: {e}")
        return False

def main():
    """Основная функция тестирования"""
    
    print("🧪 Комплексное тестирование Oracle клиента ChartGenius")
    print("=" * 60)
    
    # Тест подключения и пула
    pool_ok = test_connection_pool()
    
    if pool_ok:
        # Основные тесты
        client_ok = test_oracle_client()
        
        if client_ok:
            print("\n✅ Все тесты прошли успешно!")
            print("🚀 Oracle клиент готов к использованию в продакшене!")
        else:
            print("\n❌ Некоторые тесты не прошли")
            sys.exit(1)
    else:
        print("\n❌ Проблемы с пулом соединений")
        sys.exit(1)

if __name__ == "__main__":
    main()
