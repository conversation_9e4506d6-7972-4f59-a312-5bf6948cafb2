#!/usr/bin/env python3
"""
Тест подключения к Oracle AJD с wallet (mTLS аутентификация)
"""

import os
import oracledb
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

def test_wallet_connection():
    """Тест подключения к Oracle AJD с wallet для mTLS аутентификации"""
    
    # Параметры подключения
    username = os.getenv("ORACLE_USERNAME")
    password = os.getenv("ORACLE_PASSWORD")
    dsn = os.getenv("ORACLE_DSN")
    
    # Путь к wallet директории
    wallet_location = os.path.join(os.path.dirname(__file__), "oracle_wallet")
    wallet_password = "ChartGenius2025!"
    
    print("🔧 Параметры подключения с wallet:")
    print(f"   Username: {username}")
    print(f"   Password: {'*' * len(password) if password else 'None'}")
    print(f"   DSN: {dsn[:50]}..." if dsn and len(dsn) > 50 else f"   DSN: {dsn}")
    print(f"   Wallet location: {wallet_location}")
    print(f"   Wallet exists: {os.path.exists(wallet_location)}")
    
    # Проверяем наличие wallet файлов
    print("\n📁 Проверка wallet файлов:")
    wallet_files = ["cwallet.sso", "ewallet.p12", "tnsnames.ora", "sqlnet.ora", "ojdbc.properties"]
    for file in wallet_files:
        file_path = os.path.join(wallet_location, file)
        exists = os.path.exists(file_path)
        print(f"   {file}: {'✅' if exists else '❌'}")
        if exists:
            size = os.path.getsize(file_path)
            print(f"      Size: {size} bytes")
    
    if not all([username, password, dsn]):
        print("❌ Не все параметры подключения заданы!")
        return False
    
    if not os.path.exists(wallet_location):
        print("❌ Wallet директория не найдена!")
        return False
    
    try:
        print("\n🔍 Попытка подключения с mTLS аутентификацией...")
        
        # Подключение с wallet для mTLS (Thin mode)
        # Используем имя сервиса из tnsnames.ora вместо полной строки DSN
        service_name = "chartgenius2_high"  # Из tnsnames.ora

        connection = oracledb.connect(
            user=username,
            password=password,
            dsn=service_name,  # Имя сервиса из tnsnames.ora
            config_dir=wallet_location,  # Директория с tnsnames.ora
            wallet_location=wallet_location,  # Директория с ewallet.pem
            wallet_password=wallet_password  # Пароль wallet
        )
        
        print("✅ Подключение к Oracle AJD с mTLS успешно!")
        
        # Тест простого запроса
        with connection.cursor() as cursor:
            cursor.execute("SELECT 'Hello from Oracle AJD with mTLS!' as message FROM dual")
            result = cursor.fetchone()
            print(f"📝 Результат запроса: {result[0]}")
        
        # Тест создания коллекции
        with connection.cursor() as cursor:
            try:
                cursor.execute("SELECT COUNT(*) FROM user_tables WHERE table_name = 'CHARTGENIUS_DATA'")
                table_exists = cursor.fetchone()[0] > 0
                print(f"📊 Таблица CHARTGENIUS_DATA существует: {'✅' if table_exists else '❌'}")
            except Exception as e:
                print(f"⚠️ Ошибка проверки таблицы: {e}")
        
        connection.close()
        print("🔒 Соединение закрыто")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка подключения к Oracle AJD с mTLS: {e}")
        print(f"   Тип ошибки: {type(e).__name__}")
        
        # Дополнительная диагностика
        if "DPY-6005" in str(e):
            print("   💡 Возможные причины DPY-6005:")
            print("      - Неверные параметры wallet")
            print("      - Проблемы с сетевым подключением")
            print("      - Неправильная конфигурация mTLS")
        elif "DPY-4011" in str(e):
            print("   💡 Возможные причины DPY-4011:")
            print("      - Соединение закрыто сервером")
            print("      - Проблемы с SSL/TLS сертификатами")
            print("      - Таймаут подключения")
        
        return False

if __name__ == "__main__":
    success = test_wallet_connection()
    exit(0 if success else 1)
