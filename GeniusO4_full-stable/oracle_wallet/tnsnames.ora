chartgenius2_high = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.eu-frankfurt-1.oraclecloud.com))(connect_data=(service_name=g2fbf778b2604d0_chartgenius2_high.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

chartgenius2_low = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.eu-frankfurt-1.oraclecloud.com))(connect_data=(service_name=g2fbf778b2604d0_chartgenius2_low.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

chartgenius2_medium = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.eu-frankfurt-1.oraclecloud.com))(connect_data=(service_name=g2fbf778b2604d0_chartgenius2_medium.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

chartgenius2_tp = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.eu-frankfurt-1.oraclecloud.com))(connect_data=(service_name=g2fbf778b2604d0_chartgenius2_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))

chartgenius2_tpurgent = (description= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1522)(host=adb.eu-frankfurt-1.oraclecloud.com))(connect_data=(service_name=g2fbf778b2604d0_chartgenius2_tpurgent.adb.oraclecloud.com))(security=(ssl_server_dn_match=yes)))



