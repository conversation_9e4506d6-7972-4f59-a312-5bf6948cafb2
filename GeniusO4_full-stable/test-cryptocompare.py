#!/usr/bin/env python3
"""
Тест CryptoCompare API для диагностики проблем с получением данных
"""

import os
import asyncio
import httpx
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

API_KEY = os.getenv("CRYPTOCOMPARE_API_KEY", "")
BASE_URL = "https://min-api.cryptocompare.com/data/v2/histo"

async def test_cryptocompare_api():
    """Тестирует CryptoCompare API"""
    print("🔍 Тестирование CryptoCompare API...")
    print(f"API Key: {API_KEY[:10]}..." if API_KEY else "❌ API Key не найден!")
    
    # Параметры для тестового запроса
    url = f"{BASE_URL}hour"
    params = {
        "fsym": "BTC",
        "tsym": "USD", 
        "limit": 10,
        "aggregate": 4,
        "api_key": API_KEY
    }
    
    print(f"📡 Запрос: {url}")
    print(f"📋 Параметры: {params}")
    
    try:
        async with httpx.AsyncClient() as client:
            print("⏳ Отправляем запрос...")
            resp = await client.get(url, params=params, timeout=30)
            
            print(f"📊 Статус: {resp.status_code}")
            print(f"📏 Размер ответа: {len(resp.content)} байт")
            
            if resp.status_code == 200:
                data = resp.json()
                print("✅ Запрос успешен!")
                
                # Проверяем структуру ответа
                if "Data" in data and "Data" in data["Data"]:
                    ohlc_data = data["Data"]["Data"]
                    print(f"📈 Получено {len(ohlc_data)} свечей")
                    
                    if ohlc_data:
                        first_candle = ohlc_data[0]
                        print(f"🕯️ Первая свеча: {first_candle}")
                        
                        # Проверяем наличие необходимых полей
                        required_fields = ['time', 'open', 'high', 'low', 'close', 'volumefrom']
                        missing_fields = [field for field in required_fields if field not in first_candle]
                        
                        if missing_fields:
                            print(f"⚠️ Отсутствуют поля: {missing_fields}")
                        else:
                            print("✅ Все необходимые поля присутствуют")
                    else:
                        print("❌ Данные OHLC пусты")
                else:
                    print("❌ Неожиданная структура ответа")
                    print(f"Ответ: {data}")
            else:
                print(f"❌ Ошибка HTTP: {resp.status_code}")
                print(f"Ответ: {resp.text}")
                
    except httpx.TimeoutException:
        print("⏰ Таймаут запроса!")
    except Exception as e:
        print(f"💥 Ошибка: {e}")

if __name__ == "__main__":
    asyncio.run(test_cryptocompare_api())
