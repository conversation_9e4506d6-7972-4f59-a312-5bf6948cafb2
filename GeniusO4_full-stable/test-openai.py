#!/usr/bin/env python3
"""
Тест OpenAI API для диагностики проблем с анализом
"""

import os
import asyncio
from dotenv import load_dotenv
from openai import OpenAI

# Загружаем переменные окружения
load_dotenv()

API_KEY = os.getenv("OPENAI_API_KEY", "")

def test_openai_api():
    """Тестирует OpenAI API"""
    print("🔍 Тестирование OpenAI API...")
    print(f"API Key: {API_KEY[:20]}..." if API_KEY else "❌ API Key не найден!")
    
    if not API_KEY:
        print("❌ OPENAI_API_KEY не найден в .env файле")
        return
    
    try:
        client = OpenAI(api_key=API_KEY)
        
        # Простой тестовый запрос
        print("📡 Отправляем тестовый запрос...")
        
        messages = [
            {
                "role": "system", 
                "content": "You are a helpful assistant."
            },
            {
                "role": "user", 
                "content": "Say 'Hello, API test successful!' in JSON format: {\"message\": \"your response\"}"
            }
        ]
        
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            max_tokens=100,
            timeout=30
        )
        
        print("✅ Запрос успешен!")
        print(f"📝 Ответ: {response.choices[0].message.content}")
        print(f"🔧 Модель: {response.model}")
        
        if hasattr(response, 'usage') and response.usage:
            print(f"📊 Токены: {response.usage.total_tokens}")
        
    except Exception as e:
        print(f"💥 Ошибка OpenAI API: {e}")
        print(f"Тип ошибки: {type(e)}")

if __name__ == "__main__":
    test_openai_api()
